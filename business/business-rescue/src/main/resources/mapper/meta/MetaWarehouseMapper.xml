<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.rescue.meta.mapper.MetaWarehouseMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.rescue.meta.pojo.po.MetaWarehouse">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="dept_id" property="deptId"/>
        <result column="common" property="common"/>
        <result column="name" property="name"/>
        <result column="image" property="image"/>
        <result column="address" property="address"/>
        <result column="detail_address" property="detailAddress"/>
        <result column="location" property="location"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="enable" property="enable"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, dept_id, common, name, image, address, detail_address, location, longitude, latitude, enable, create_time, update_time
    </sql>

</mapper>
