<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.rescue.work.mapper.WorkMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.rescue.work.pojo.po.Work">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="dept_id" property="deptId"/>
        <result column="category_id" property="categoryId"/>
        <result column="work_number" property="workNumber"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="apply_require" property="applyRequire"/>
        <result column="principal_user_id" property="principalUserId"/>
        <result column="principal_name" property="principalName"/>
        <result column="principal_phone" property="principalPhone"/>
        <result column="ask_name" property="askName"/>
        <result column="ask_contact" property="askContact"/>
        <result column="address" property="address"/>
        <result column="detail_address" property="detailAddress"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="remark" property="remark"/>
        <result column="enable" property="enable"/>
        <result column="status" property="status"/>
        <result column="enable_check_address" property="enableCheckAddress"/>
        <result column="enable_check_password" property="enableCheckPassword"/>
        <result column="enable_check_time" property="enableCheckTime"/>
        <result column="sign_on_password" property="signOnPassword"/>
        <result column="sign_exit_password" property="signExitPassword"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, dept_id, category_id, work_number, title, content, apply_require, principal_user_id, principal_name, principal_phone, ask_name, ask_contact, address, detail_address, longitude, latitude, remark, enable, status, enable_check_address, enable_check_password, enable_check_time, sign_on_password, sign_exit_password, start_time, end_time, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
