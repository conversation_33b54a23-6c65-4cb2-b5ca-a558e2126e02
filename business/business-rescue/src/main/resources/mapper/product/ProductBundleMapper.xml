<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.rescue.product.mapper.ProductBundleMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.rescue.product.pojo.po.ProductBundle">
        <id column="id" property="id"/>
        <result column="product_id" property="productId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="dept_id" property="deptId"/>
        <result column="sku_id" property="skuId"/>
        <result column="quantity" property="quantity"/>
        <result column="enable" property="enable"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product_id, tenant_id, dept_id, sku_id, quantity, enable, create_time, update_time
    </sql>

</mapper>
