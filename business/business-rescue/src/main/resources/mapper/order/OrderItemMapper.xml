<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.rescue.order.mapper.OrderItemMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.rescue.order.pojo.po.OrderItem">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="dept_id" property="deptId"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="image" property="image"/>
        <result column="sku_id" property="skuId"/>
        <result column="sku_name" property="skuName"/>
        <result column="sku_image" property="skuImage"/>
        <result column="quantity" property="quantity"/>
        <result column="price" property="price"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, tenant_id, dept_id, product_id, product_name, image, sku_id, sku_name, sku_image, quantity, price, create_time, update_time
    </sql>

</mapper>
