<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.rescue.order.mapper.OrderMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.rescue.order.pojo.po.Order">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="dept_id" property="deptId"/>
        <result column="work_id" property="workId"/>
        <result column="work_number" property="workNumber"/>
        <result column="type" property="type"/>
        <result column="reason" property="reason"/>
        <result column="apply_user_id" property="applyUserId"/>
        <result column="apply_name" property="applyName"/>
        <result column="apply_phone" property="applyPhone"/>
        <result column="operate" property="operate"/>
        <result column="review_user_id" property="reviewUserId"/>
        <result column="review_name" property="reviewName"/>
        <result column="review_phone" property="reviewPhone"/>
        <result column="review_opinion" property="reviewOpinion"/>
        <result column="total_price" property="totalPrice"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, dept_id, work_id, work_number, type, reason, apply_user_id, apply_name, apply_phone, operate, review_user_id, review_name, review_phone, review_opinion, total_price, total_amount, status, remark, create_time, update_time
    </sql>

</mapper>
