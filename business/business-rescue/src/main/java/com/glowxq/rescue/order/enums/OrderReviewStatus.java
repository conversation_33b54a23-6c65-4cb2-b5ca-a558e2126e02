package com.glowxq.rescue.order.enums;

import com.glowxq.core.common.enums.base.BaseType;
import com.glowxq.core.common.exception.common.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 物资订单审批状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/20
 */
@AllArgsConstructor
@Getter
public enum OrderReviewStatus implements BaseType {

    // 出库申请
    // 出库审核通过
    // 出库完成
    // 入库申请
    // 入库审核通过
    // 入库完成
    // 流程终止
    None("None", 0, "无状态"),
    OutboundApply("OutboundApply", 1, "出库申请"),
    OutboundApprove("OutboundApprove", 2, "出库审核通过"),
    OutboundComplete("OutboundComplete", 3, "出库完成"),
    InboundApply("InboundApply", 4, "入库申请"),
    InboundApprove("InboundApprove", 5, "入库审核通过"),
    InboundComplete("InboundComplete", 6, "入库完成"),
    Terminate("Terminate", 7, "流程终止");;

    /**
     * code
     */
    private final String code;

    /**
     * 索引
     */
    private final Integer index;

    /**
     * 最近类型
     */
    private final String name;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static OrderReviewStatus matchCode(String code) {
        for (OrderReviewStatus orderReviewStatus : OrderReviewStatus.values()) {
            if (orderReviewStatus.getCode().equals(code)) {
                return orderReviewStatus;
            }
        }
        return null;
    }

    /**
     * 根据index获取枚举
     *
     * @param index
     * @return
     */
    public static OrderReviewStatus matchIndex(Integer index) {
        if (index == null || index < 0 || index > OrderReviewStatus.Terminate.getIndex()) {
            throw new BusinessException("无效的[OrderReviewStatus]状态索引 index:" + index);
        }
        for (OrderReviewStatus orderReviewStatus : OrderReviewStatus.values()) {
            if (orderReviewStatus.getIndex().equals(index)) {
                return orderReviewStatus;
            }
        }
        throw new BusinessException("无效的[OrderReviewStatus]状态索引 index:" + index);
    }
}
