package com.glowxq.rescue.work.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.work.pojo.po.WorkSign;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * WorkSign修改DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Schema(description = "WorkSign修改DTO")
public class WorkSignUpdateDTO implements BaseDTO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "任务ID")
    private Long workId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "签到/签退")
    private String signType;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "签到日期")
    private LocalDate signDate;

    @Schema(description = "签到时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime signTime;

    @Override
    public WorkSign buildEntity() {
        return BeanCopyUtils.copy(this, WorkSign.class);
    }
}