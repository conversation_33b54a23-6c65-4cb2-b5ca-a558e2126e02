package com.glowxq.rescue.product.service;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.rescue.product.pojo.dto.ProductLotCreateDTO;
import com.glowxq.rescue.product.pojo.dto.ProductLotListDTO;
import com.glowxq.rescue.product.pojo.dto.ProductLotOverhaulDTO;
import com.glowxq.rescue.product.pojo.dto.ProductLotUpdateDTO;
import com.glowxq.rescue.product.pojo.po.ProductLot;
import com.glowxq.rescue.product.pojo.vo.ProductLotVO;
import com.mybatisflex.core.service.IService;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 物资批次 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface ProductLotService extends IService<ProductLot> {

    void create(ProductLotCreateDTO dto);

    void update(ProductLotUpdateDTO dto);

    PageResult<ProductLotVO> page(ProductLotListDTO dto);

    List<ProductLotVO> list(ProductLotListDTO dto);

    void remove(SelectIdsDTO dto);

    ProductLotVO detail(Long id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(ProductLotListDTO dto, HttpServletResponse response);

    void deleteByProductId(Long productId);

    void deleteBySkuId(Long id);

    void overhaul(ProductLotOverhaulDTO dto);
}