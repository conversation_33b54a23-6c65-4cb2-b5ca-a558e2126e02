package com.glowxq.rescue.order.pojo.po;

import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.mysql.EntityChangeListener;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.time.LocalDateTime;

/**
* <p>
* 物资审批记录
* </p>
*
* <AUTHOR>
* @since 2025-06-16
*/
@Data
@Table(value = "order_review", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "物资审批记录")
public class OrderReview implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description ="记录ID")
    private Long id;

    @Schema(description ="租户ID")
    private String tenantId;

    @Schema(description ="部门ID")
    private Long deptId;

    @Schema(description ="物资记录ID")
    private Long orderId;

    @Schema(description ="审批用户ID")
    private Long reviewUserId;

    @Schema(description ="审批人")
    private String reviewName;

    @Schema(description ="审批人电话")
    private String reviewPhone;

    @Schema(description ="原状态")
    private String originStatus;

    @Schema(description ="审批后状态")
    private String targetStatus;

    @Schema(description ="审批意见")
    private String reviewOpinion;

    @Schema(description ="创建时间")
    private LocalDateTime createTime;

    @Schema(description ="更新时间")
    private LocalDateTime updateTime;

}