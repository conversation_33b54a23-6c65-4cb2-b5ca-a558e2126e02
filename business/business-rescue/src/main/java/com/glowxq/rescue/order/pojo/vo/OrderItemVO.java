package com.glowxq.rescue.order.pojo.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * OrderItem返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "OrderItem返回vo")
public class OrderItemVO implements BaseVO {

    @ExcelIgnore
    @Schema(description = "详情ID")
    private Long id;

    @ExcelProperty(value = "订单ID")
    @Schema(description = "订单ID")
    private Long orderId;

    @ExcelProperty(value = "租户ID")
    @Schema(description = "租户ID")
    private String tenantId;

    @ExcelProperty(value = "部门id")
    @Schema(description = "部门id")
    private Long deptId;

    @ExcelProperty(value = "物资id")
    @Schema(description = "物资id")
    private Long productId;

    @ExcelProperty(value = "物资名")
    @Schema(description = "物资名")
    private String productName;

    @ExcelProperty(value = "物资图片")
    @Schema(description = "物资图片")
    private String image;

    @ExcelProperty(value = "规格名")
    @Schema(description = "规格名")
    private Long skuId;

    @ExcelProperty(value = "规格名")
    @Schema(description = "规格名")
    private String skuName;

    @ExcelProperty(value = "物资规格图片")
    @Schema(description = "物资规格图片")
    private String skuImage;

    @ExcelProperty(value = "数量")
    @Schema(description = "数量")
    private Integer quantity;

    @ExcelProperty(value = "单价")
    @Schema(description = "单价")
    private BigDecimal price;
}