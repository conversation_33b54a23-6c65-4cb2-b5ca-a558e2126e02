package com.glowxq.rescue.user.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.DateUtils;
import com.glowxq.rescue.user.pojo.po.UserInfo;
import com.glowxq.system.admin.pojo.dto.sysuser.SysUserCreateDTO;
import com.glowxq.system.base.user.UserDTO;
import com.glowxq.system.enums.DictUserTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * UserInfo添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Schema(description = "UserInfo添加DTO")
public class UserInfoRegisterDTO implements BaseDTO, UserDTO {

    @Schema(description = "队伍ID")
    private Long deptId;

    @Schema(description = "微信登录code")
    private String wechatCode;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "编号前缀")
    private String numberPrefix;

    @Schema(description = "编号")
    private Integer number;

    @Schema(description = "职务")
    private String post;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "身份证号")
    private String identityCard;

    @Schema(description = "身份证有效期开始")
    private LocalDate identityStartDate;

    @Schema(description = "身份证有效期截止")
    private LocalDate identityEndDate;

    @Schema(description = "护照号")
    private String passportNumber;

    @Schema(description = "保险状态")
    private Boolean insuranceStatus;

    @Schema(description = "政治面貌")
    private String politicsStatus;

    @Schema(description = "血型")
    private String bloodType;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "生日")
    private LocalDate birthday;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "签名图片")
    private String signatureImage;

    @Schema(description = "身份证图片")
    private String identityImage;

    @Schema(description = "资料图片多个")
    private String informationImage;

    @Schema(description = "紧急联系人")
    private String emergencyContact;

    @Schema(description = "紧急联系人电话")
    private String emergencyContactPhone;

    @Schema(description = "医疗史")
    private String medicalHistory;

    @Schema(description = "过敏史")
    private String allergiesHistory;

    @Schema(description = "入队时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;

    @Override
    public UserInfo buildEntity() {
        return BeanCopyUtils.copy(this, UserInfo.class);
    }

    @Override
    public SysUserCreateDTO buildSysUserDTO() {
        SysUserCreateDTO sysUserCreateDTO = new SysUserCreateDTO();
        sysUserCreateDTO.setUsername(phone);
        sysUserCreateDTO.setPhone(phone);
        sysUserCreateDTO.setName(name);
        sysUserCreateDTO.setNickname(nickName);
        sysUserCreateDTO.setDeptId(deptId);
        sysUserCreateDTO.setOpenid("");
        sysUserCreateDTO.setLogo(avatar);
        sysUserCreateDTO.setIdCard(identityCard);
        sysUserCreateDTO.setDataScope("");
        sysUserCreateDTO.setEmail("");
        sysUserCreateDTO.setUserTagCd(DictUserTypeEnum.Normal.getCode());
        sysUserCreateDTO.setBirthday(DateUtils.formatDateTime(birthday));
        return sysUserCreateDTO;
    }
}