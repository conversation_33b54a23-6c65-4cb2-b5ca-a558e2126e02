package com.glowxq.rescue.product.service.impl;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.PageUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.rescue.common.enums.RescueTagBind;
import com.glowxq.rescue.order.pojo.bo.SkuQuantityBO;
import com.glowxq.rescue.product.mapper.ProductMapper;
import com.glowxq.rescue.product.pojo.bo.SkuOrderDetailBO;
import com.glowxq.rescue.product.pojo.dto.ProductCreateDTO;
import com.glowxq.rescue.product.pojo.dto.ProductImportDTO;
import com.glowxq.rescue.product.pojo.dto.ProductListDTO;
import com.glowxq.rescue.product.pojo.dto.ProductUpdateDTO;
import com.glowxq.rescue.product.pojo.po.Product;
import com.glowxq.rescue.product.pojo.po.ProductLot;
import com.glowxq.rescue.product.pojo.po.ProductSku;
import com.glowxq.rescue.product.pojo.vo.ProductSelectVO;
import com.glowxq.rescue.product.pojo.vo.ProductVO;
import com.glowxq.rescue.product.service.ProductLotService;
import com.glowxq.rescue.product.service.ProductService;
import com.glowxq.rescue.product.service.ProductSkuService;
import com.glowxq.system.meta.pojo.po.MetaTag;
import com.glowxq.system.meta.service.MetaTagService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.OutputStream;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 物资品类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Service
@RequiredArgsConstructor
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    private final ProductSkuService productSkuService;

    private final ProductLotService productLotService;

    private final MetaTagService metaTagService;

    private static QueryWrapper buildQueryWrapper(ProductListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(Product.class);
        wrapper.eq(Product::getTenantId, dto.getTenantId(), Utils.isNotNull(dto.getTenantId()));
        wrapper.eq(Product::getDeptId, dto.getDeptId(), Utils.isNotNull(dto.getDeptId()));
        wrapper.eq(Product::getCategoryId, dto.getCategoryId(), Utils.isNotNull(dto.getCategoryId()));
        wrapper.eq(Product::getWarehouseId, dto.getWarehouseId(), Utils.isNotNull(dto.getWarehouseId()));
        wrapper.like(Product::getName, dto.getName(), Utils.isNotNull(dto.getName()));
        wrapper.eq(Product::getImage, dto.getImage(), Utils.isNotNull(dto.getImage()));
        wrapper.eq(Product::getProductNumber, dto.getProductNumber(), Utils.isNotNull(dto.getProductNumber()));
        wrapper.eq(Product::getEnable, dto.getEnable(), Utils.isNotNull(dto.getEnable()));
        return wrapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProductCreateDTO dto) {
        Product product = dto.buildEntity();
        save(product);
        List<ProductSku> productSkus = dto.buildProductSkus(product.getId());
        productSkuService.saveBatch(productSkus);

        List<ProductLot> productLots = productSkus.stream().map(e -> e.buildProductLots(product.getId())).flatMap(Collection::stream).toList();
        productLotService.saveBatch(productLots);
        metaTagService.bindTags(product.getId(), dto.getTagIds(), RescueTagBind.Product);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProductUpdateDTO dto) {
        Product product = BeanCopyUtils.copy(dto, Product.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create().eq(Product::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);
        saveOrUpdate(product);

        productSkuService.deleteByProductId(dto.getId());
        List<ProductSku> productSkus = dto.buildProductSkus(product.getId());
        productSkuService.saveBatch(productSkus);

        productLotService.deleteByProductId(dto.getId());
        List<ProductLot> productLots = productSkus.stream().map(e -> e.buildProductLots(product.getId())).flatMap(Collection::stream).toList();
        productLotService.saveBatch(productLots);

        metaTagService.unBindAll(product.getId(), RescueTagBind.Product);
        metaTagService.bindTags(product.getId(), dto.getTagIds(), RescueTagBind.Product);
    }

    @Override
    public PageResult<ProductVO> page(ProductListDTO dto) {
        QueryWrapper wrapper = buildQueryWrapper(dto);
        if (CollectionUtils.isNotEmpty(dto.getTagIds())) {
            List<Long> productIds = metaTagService.listBusinessIdByTagIds(dto.getTagIds(), RescueTagBind.Product);
            wrapper.in(Product::getId, productIds, CollectionUtils.isNotEmpty(productIds));
        }

        Page<ProductVO> page = pageAs(PageUtils.getPage(dto), wrapper, ProductVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<ProductVO> list(ProductListDTO dto) {
        return listAs(buildQueryWrapper(dto), ProductVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
        for (Long id : dto.getIds()) {
            metaTagService.unBindAll(id, RescueTagBind.Product);
        }
    }

    @Override
    public ProductVO detail(Long id) {
        ProductVO productVo = mapper.selectOneWithRelationsByIdAs(id, ProductVO.class);
        CommonResponseEnum.INVALID_ID.assertNull(productVo);
        List<MetaTag> metaTags = metaTagService.listByBusinessId(id, RescueTagBind.Product);
        productVo.setTags(metaTags);
        return productVo;
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<ProductImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), ProductImportDTO.class, true);
        List<ProductImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(ProductListDTO dto, HttpServletResponse response) {
        List<ProductVO> list = list(dto);
        String fileName = "物资品类模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "物资品类", ProductVO.class, os);
    }

    @Override
    public void deductStock(List<SkuQuantityBO> skuQuantityBos) {
        Map<Long, Integer> applySkuMap = skuQuantityBos.stream().collect(Collectors.toMap(SkuQuantityBO::getSkuId, SkuQuantityBO::getQuantity));
        List<ProductSku> productSkus = productSkuService.listByIds(applySkuMap.keySet());
        List<ProductSku> deductStockSkus = productSkus.stream().peek(productSku -> {
            Integer quantity = applySkuMap.get(productSku.getId());
            productSku.deductStock(quantity);
        }).toList();
        productSkuService.updateBatch(deductStockSkus);
    }

    @Override
    public List<SkuOrderDetailBO> packSkuOrderDetail(List<SkuQuantityBO> applyProductSkus) {
        Map<Long, Integer> applySkuMap = applyProductSkus.stream().collect(Collectors.toMap(SkuQuantityBO::getSkuId, SkuQuantityBO::getQuantity));
        List<ProductSku> productSkus = productSkuService.listByIds(applySkuMap.keySet());
        List<Long> productIds = productSkus.stream().map(ProductSku::getProductId).toList();
        List<Product> products = mapper.selectListByIds(productIds);
        Map<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, Function.identity()));
        return productSkus.stream()
                          .map(productSku -> new SkuOrderDetailBO(productSku, productMap.get(productSku.getProductId()), applySkuMap.get(productSku.getId())))
                          .toList();
    }

    @Override
    public void addStock(List<SkuQuantityBO> skuQuantityBos) {
        Map<Long, Integer> applySkuMap = skuQuantityBos.stream().collect(Collectors.toMap(SkuQuantityBO::getSkuId, SkuQuantityBO::getQuantity));
        List<ProductSku> productSkus = productSkuService.listByIds(applySkuMap.keySet());
        List<ProductSku> deductStockSkus = productSkus.stream().peek(productSku -> {
            Integer quantity = applySkuMap.get(productSku.getId());
            productSku.addStock(quantity);
        }).toList();
        productSkuService.updateBatch(deductStockSkus);
    }

    @Override
    public List<ProductSku> checkStock(List<SkuQuantityBO> skuQuantityBos) {
        Map<Long, Integer> applySkuMap = skuQuantityBos.stream().collect(Collectors.toMap(SkuQuantityBO::getSkuId, SkuQuantityBO::getQuantity));
        List<ProductSku> productSkus = productSkuService.listByIds(applySkuMap.keySet());
        List<ProductSku> deductStockSkus = productSkus.stream().peek(productSku -> {
            Integer quantity = applySkuMap.get(productSku.getId());
            productSku.deductStock(quantity);
        }).toList();
        return deductStockSkus;
    }

    @Override
    public PageResult<ProductSelectVO> pageSelect(ProductListDTO dto) {
        QueryWrapper wrapper = buildQueryWrapper(dto);
        if (CollectionUtils.isNotEmpty(dto.getTagIds())) {
            List<Long> productIds = metaTagService.listBusinessIdByTagIds(dto.getTagIds(), RescueTagBind.Product);
            wrapper.in(Product::getId, productIds, CollectionUtils.isNotEmpty(productIds));
        }
        Page<ProductSelectVO> page = mapper.paginateWithRelationsAs(PageUtils.getPage(dto), wrapper, ProductSelectVO.class);
        return PageUtils.getPageResult(page);
    }
}