package com.glowxq.rescue.product.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.NumberUtils;
import com.glowxq.rescue.common.enums.NumberPrefixEnum;
import com.glowxq.rescue.product.pojo.dto.inside.ProductSkuInsideDTO;
import com.glowxq.rescue.product.pojo.po.Product;
import com.glowxq.rescue.product.pojo.po.ProductSku;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <p>
 * Product添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "Product添加DTO")
public class ProductCreateDTO implements BaseDTO {

    @Schema(description = "分类id")
    private Long categoryId;

    @Schema(description = "仓库id")
    private Long warehouseId;

    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description = "图片")
    private String image;

    @Schema(description = "物资编号")
    private String productNumber;

    @Schema(description = "启用")
    private Boolean enable;

    @Schema(description = "标签ID")
    private List<Long> tagIds;

    /**
     * 物资规格
     */
    @Size(min = 1, message = "至少要添加一个规格")
    private List<ProductSkuInsideDTO> productSkus;

    @Override
    public Product buildEntity() {
        Product product = BeanCopyUtils.copy(this, Product.class);
        product.setProductNumber(NumberUtils.generateNumber(NumberPrefixEnum.Product));
        return product;
    }

    public List<ProductSku> buildProductSkus(Long productId) {
        if (CollectionUtils.isEmpty(productSkus)) {
            return List.of();
        }
        return productSkus.stream().map(ProductSkuInsideDTO::buildEntity).peek(e -> e.setProductId(productId)).toList();
    }
}