package com.glowxq.rescue.order.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.order.pojo.bo.SkuQuantityBO;
import com.glowxq.rescue.order.pojo.po.Order;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * Order添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "Order添加DTO")
public class OrderApplyDTO implements BaseDTO {

    @Schema(description = "任务编号")
    private String workNumber;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "操作原因")
    private String reason;

    @Schema(description = "出入库")
    private Boolean operate;

    @Schema(description = "备注")
    private String remark;

    @Size(min = 1, max = 100, message = "最多只能选择100个规格")
    @Schema(description = "skuId")
    private List<SkuQuantityBO> productSkus;

    @Override
    public Order buildEntity() {
        return BeanCopyUtils.copy(this, Order.class);
    }
}