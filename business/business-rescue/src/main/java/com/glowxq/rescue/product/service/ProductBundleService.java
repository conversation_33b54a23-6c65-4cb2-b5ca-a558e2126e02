package com.glowxq.rescue.product.service;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.rescue.product.pojo.dto.ProductBundleCreateDTO;
import com.glowxq.rescue.product.pojo.dto.ProductBundleListDTO;
import com.glowxq.rescue.product.pojo.dto.ProductBundleUpdateDTO;
import com.glowxq.rescue.product.pojo.po.ProductBundle;
import com.glowxq.rescue.product.pojo.vo.ProductBundleVO;
import com.mybatisflex.core.service.IService;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 物资包 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface ProductBundleService extends IService<ProductBundle> {

    void create(ProductBundleCreateDTO dto);

    void update(ProductBundleUpdateDTO dto);

    PageResult<ProductBundleVO> page(ProductBundleListDTO dto);

    List<ProductBundleVO> list(ProductBundleListDTO dto);

    void remove(SelectIdsDTO dto);

    ProductBundleVO detail(Long id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(ProductBundleListDTO dto, HttpServletResponse response);
}