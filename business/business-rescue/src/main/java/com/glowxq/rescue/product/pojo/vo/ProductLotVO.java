package com.glowxq.rescue.product.pojo.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseVO;
import com.mybatisflex.annotation.RelationOneToOne;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * ProductLot返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "ProductLot返回vo")
public class ProductLotVO implements BaseVO{

    @ExcelIgnore
    @Schema(description =  "批次ID")
    private Long id;

    @ExcelProperty(value = "租户id")
    @Schema(description =  "租户id")
    private String tenantId;

    @ExcelProperty(value = "部门id")
    @Schema(description =  "部门id")
    private Long deptId;

    @ExcelProperty(value = "物资ID")
    @Schema(description =  "物资ID")
    private Long productId;

    @ExcelProperty(value = "规格id")
    @Schema(description =  "规格id")
    private Long skuId;

    @ExcelProperty(value = "物资名")
    @Schema(description = "物资名")
    @RelationOneToOne(selfField = "productId", targetField = "id", targetTable = "product", valueField = "name")
    private String productName;

    @ExcelProperty(value = "规格")
    @Schema(description = "规格")
    @RelationOneToOne(selfField = "skuId", targetField = "id", targetTable = "product_sku", valueField = "name")
    private String skuName;

    @ExcelProperty(value = "批次编号")
    @Schema(description =  "批次编号")
    private String lotNumber;

    @ExcelProperty(value = "批次标题")
    @Schema(description =  "批次标题")
    private String title;

    @ExcelProperty(value = "价格 ")
    @Schema(description =  "价格 ")
    private BigDecimal price;

    @ExcelProperty(value = "入库数量")
    @Schema(description =  "入库数量")
    private Integer quantity;

    @ExcelProperty(value = "检修间隔/天")
    @Schema(description = "检修间隔/天")
    private Integer overhaulGap;

    @ExcelProperty(value = "入库时间")
    @Schema(description =  "入库时间")
    private LocalDate storageDate;

    @ExcelProperty(value = "生产日期")
    @Schema(description =  "生产日期")
    private LocalDate productDate;

    @ExcelProperty(value = "过期时间")
    @Schema(description =  "过期时间")
    private LocalDate expirationTime;

    @ExcelProperty(value = "报废日期")
    @Schema(description =  "报废日期")
    private LocalDate discardDate;

    @ExcelProperty(value = "最后检修时间")
    @Schema(description = "最后检修时间")
    private LocalDate overhaulLastDate;

    @ExcelProperty(value = "下次检修时间")
    @Schema(description = "下次检修时间")
    private LocalDate overhaulNextDate;
}