package com.glowxq.rescue.work.mapper;

import com.glowxq.rescue.work.pojo.po.WorkUserApply;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;

/**
 * <p>
 * 报名申请 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface WorkUserApplyMapper extends BaseMapper<WorkUserApply> {

    default WorkUserApply getWorkIdUserId(Long workId, Long userId) {
        QueryWrapper qw = QueryWrapper.create();
        qw.eq(WorkUserApply::getWorkId, workId);
        qw.eq(WorkUserApply::getUserId, userId);
        return this.selectOneByQuery(qw);
    }
}