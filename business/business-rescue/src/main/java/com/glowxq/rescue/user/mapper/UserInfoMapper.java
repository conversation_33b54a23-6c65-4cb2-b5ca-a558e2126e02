package com.glowxq.rescue.user.mapper;

import com.glowxq.rescue.user.pojo.po.UserInfo;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 用户信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
public interface UserInfoMapper extends BaseMapper<UserInfo> {

    /**
     * 获取最大编号
     *
     * @return
     */
    @Select("select max(number) from user_info where dept_id = #{deptId}")
    Integer getMaxByDeptNumber(Long deptId);
}