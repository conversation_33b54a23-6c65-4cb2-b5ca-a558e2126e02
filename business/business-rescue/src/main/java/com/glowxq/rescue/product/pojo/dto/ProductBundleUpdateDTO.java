package com.glowxq.rescue.product.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.product.pojo.po.ProductBundle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * ProductBundle修改DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "ProductBundle修改DTO")
public class ProductBundleUpdateDTO implements BaseDTO {

    @Schema(description = "规格ID")
    private Long id;

    @Schema(description = "物资id")
    private Long productId;

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "规格ID")
    private Long skuId;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "启用")
    private Boolean enable;

    @Override
    public ProductBundle buildEntity() {
        return BeanCopyUtils.copy(this, ProductBundle.class);
    }
}