package com.glowxq.rescue.product.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.rescue.product.pojo.dto.ProductLotCreateDTO;
import com.glowxq.rescue.product.pojo.dto.ProductLotListDTO;
import com.glowxq.rescue.product.pojo.dto.ProductLotOverhaulDTO;
import com.glowxq.rescue.product.pojo.dto.ProductLotUpdateDTO;
import com.glowxq.rescue.product.pojo.vo.ProductLotVO;
import com.glowxq.rescue.product.service.ProductLotService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * product/物资批次 Api
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Tag(name = "物资批次")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class ProductLotController extends BaseApi {

    private final ProductLotService productLotService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "product.lot.create")
    @PostMapping("/product-lot/create")
    public ApiResult<Void> create(@RequestBody ProductLotCreateDTO dto) {
        productLotService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "product.lot.update")
    @PutMapping("/product-lot/update")
    public ApiResult<Void> update(@RequestBody ProductLotUpdateDTO dto) {
        productLotService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "检修")
    @SaCheckPermission(value = "product.lot.update")
    @PutMapping("/product-lot/overhaul")
    public ApiResult<Void> overhaul(@RequestBody ProductLotOverhaulDTO dto) {
        productLotService.overhaul(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "product.lot.remove")
    @DeleteMapping("/product-lot/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        productLotService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "product.lot.query_table")
    @GetMapping("/product-lot/list")
    public ApiResult<PageResult<ProductLotVO>> list(ProductLotListDTO dto) {
        return ApiPageResult.success(productLotService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "product.lot.query_table")
    @GetMapping("/product-lot/detail")
    public ApiResult<ProductLotVO> detail(@RequestParam Long id) {
        return ApiResult.success(productLotService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "product.lot.import")
    @PostMapping("/product-lot/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        productLotService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "product.lot.export")
    @PostMapping("/product-lot/export")
    public void exportExcel(@RequestBody ProductLotListDTO dto, HttpServletResponse response) {
        productLotService.exportExcel(dto, response);
    }
}