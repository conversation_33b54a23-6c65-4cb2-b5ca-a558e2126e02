package com.glowxq.rescue.product.pojo.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseVO;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.RelationOneToOne;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * ProductSku返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "ProductSku返回vo")
public class ProductSkuVO implements BaseVO {

    @ExcelIgnore
    @Schema(description = "规格ID")
    private Long id;

    @ExcelProperty(value = "物资id")
    @Schema(description = "物资id")
    private Long productId;

    @ExcelProperty(value = "租户id")
    @Schema(description = "租户id")
    private String tenantId;

    @ExcelProperty(value = "部门id")
    @Schema(description = "部门id")
    private Long deptId;

    @RelationOneToOne(selfField = "productId", targetField = "id", targetTable = "product", valueField = "name")
    @ExcelProperty(value = "物资名")
    @Schema(description = "物资名")
    private String productName;

    @Schema(description = "物资图片")
    @RelationOneToOne(selfField = "productId", targetField = "id", targetTable = "product", valueField = "name")
    private String productImage;

    @Schema(description = "规格图片")
    private String image;

    @ExcelProperty(value = "规格名称")
    @Schema(description = "规格名称")
    private String name;

    @ExcelProperty(value = "库存")
    @Schema(description = "库存")
    private Integer stock;

    @ExcelProperty(value = "价格")
    @Schema(description = "价格")
    private BigDecimal price;

    @ExcelProperty(value = "启用")
    @Schema(description = "启用")
    private Boolean enable;

    @RelationOneToMany(selfField = "id", targetField = "skuId", targetTable = "product_lot")
    private List<ProductLotVO> productLots;
}