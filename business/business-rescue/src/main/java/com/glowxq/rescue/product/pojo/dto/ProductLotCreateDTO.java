package com.glowxq.rescue.product.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.NumberUtils;
import com.glowxq.rescue.common.enums.NumberPrefixEnum;
import com.glowxq.rescue.product.pojo.po.ProductLot;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * ProductLot添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "ProductLot添加DTO")
public class ProductLotCreateDTO implements BaseDTO {

    @Schema(description = "物资ID")
    private Long productId;

    @Schema(description = "规格id")
    private Long skuId;

    @Schema(description = "批次编号")
    private String lotNumber;

    @Schema(description = "批次标题")
    private String title;

    @Schema(description = "价格 ")
    private BigDecimal price;

    @Schema(description = "入库数量")
    private Integer quantity;

    @Schema(description = "入库时间")
    private LocalDate storageDate;

    @Schema(description = "生产日期")
    private LocalDate productDate;

    @Schema(description = "过期时间")
    private LocalDate expirationTime;

    @Schema(description = "报废日期")
    private LocalDate discardDate;

    @Schema(description = "最后检修时间")
    private LocalDate overhaulLastDate;

    @Schema(description = "下次检修时间")
    private LocalDate overhaulNextDate;

    @Schema(description = "检修间隔/天")
    private Integer overhaulGap;

    @Override
    public ProductLot buildEntity() {
        ProductLot productLot = BeanCopyUtils.copy(this, ProductLot.class);
        productLot.setLotNumber(NumberUtils.generateNumber(NumberPrefixEnum.ProductLot));
        return productLot;
    }
}