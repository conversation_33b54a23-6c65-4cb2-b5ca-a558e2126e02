package com.glowxq.rescue.order.pojo.bo;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.order.pojo.po.Order;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * Order添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "Order添加DTO")
@NoArgsConstructor
@AllArgsConstructor
public class SkuQuantityBO implements BaseDTO {

    @Schema(description = "skuId")
    private Long skuId;

    @Schema(description = "数量")
    private Integer quantity;

    @Override
    public Order buildEntity() {
        return BeanCopyUtils.copy(this, Order.class);
    }
}