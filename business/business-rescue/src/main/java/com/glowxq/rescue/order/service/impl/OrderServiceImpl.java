package com.glowxq.rescue.order.service.impl;

import com.glowxq.core.common.entity.BaseUserInfo;
import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.common.exception.common.BusinessException;
import com.glowxq.core.util.*;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.rescue.order.enums.OrderReviewStatus;
import com.glowxq.rescue.order.mapper.OrderMapper;
import com.glowxq.rescue.order.pojo.bo.SkuQuantityBO;
import com.glowxq.rescue.order.pojo.dto.*;
import com.glowxq.rescue.order.pojo.po.Order;
import com.glowxq.rescue.order.pojo.po.OrderItem;
import com.glowxq.rescue.order.pojo.po.OrderReview;
import com.glowxq.rescue.order.pojo.vo.OrderVO;
import com.glowxq.rescue.order.service.OrderItemService;
import com.glowxq.rescue.order.service.OrderReviewService;
import com.glowxq.rescue.order.service.OrderService;
import com.glowxq.rescue.product.pojo.bo.SkuOrderDetailBO;
import com.glowxq.rescue.product.pojo.po.ProductSku;
import com.glowxq.rescue.product.service.ProductService;
import com.glowxq.rescue.work.pojo.po.Work;
import com.glowxq.rescue.work.service.WorkService;
import com.glowxq.security.core.util.LoginUtils;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 物资订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Service
@RequiredArgsConstructor
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {

    private final ProductService productService;

    private final OrderItemService orderItemService;

    private final OrderReviewService orderReviewService;

    private final WorkService workService;

    private static QueryWrapper buildQueryWrapper(OrderListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(Order.class);
        wrapper.eq(Order::getTenantId, dto.getTenantId(), Utils.isNotNull(dto.getTenantId()));
        wrapper.eq(Order::getDeptId, dto.getDeptId(), Utils.isNotNull(dto.getDeptId()));
        wrapper.eq(Order::getWorkId, dto.getWorkId(), Utils.isNotNull(dto.getWorkId()));
        wrapper.eq(Order::getWorkNumber, dto.getWorkNumber(), Utils.isNotNull(dto.getWorkNumber()));
        wrapper.eq(Order::getOrderNumber, dto.getOrderNumber(), Utils.isNotNull(dto.getOrderNumber()));
        wrapper.eq(Order::getType, dto.getType(), Utils.isNotNull(dto.getType()));
        wrapper.eq(Order::getReason, dto.getReason(), Utils.isNotNull(dto.getReason()));
        wrapper.eq(Order::getApplyUserId, dto.getApplyUserId(), Utils.isNotNull(dto.getApplyUserId()));
        wrapper.like(Order::getApplyName, dto.getApplyName(), Utils.isNotNull(dto.getApplyName()));
        wrapper.eq(Order::getApplyPhone, dto.getApplyPhone(), Utils.isNotNull(dto.getApplyPhone()));
        wrapper.eq(Order::getOperate, dto.getOperate(), Utils.isNotNull(dto.getOperate()));
        wrapper.eq(Order::getReviewUserId, dto.getReviewUserId(), Utils.isNotNull(dto.getReviewUserId()));
        wrapper.like(Order::getReviewName, dto.getReviewName(), Utils.isNotNull(dto.getReviewName()));
        wrapper.eq(Order::getReviewPhone, dto.getReviewPhone(), Utils.isNotNull(dto.getReviewPhone()));
        wrapper.eq(Order::getReviewOpinion, dto.getReviewOpinion(), Utils.isNotNull(dto.getReviewOpinion()));
        wrapper.eq(Order::getTotalPrice, dto.getTotalPrice(), Utils.isNotNull(dto.getTotalPrice()));
        wrapper.eq(Order::getTotalAmount, dto.getTotalAmount(), Utils.isNotNull(dto.getTotalAmount()));
        wrapper.eq(Order::getStatus, dto.getStatus(), Utils.isNotNull(dto.getStatus()));
        wrapper.in(Order::getStatus, dto.getStatusList(), CollectionUtils.isNotEmpty(dto.getStatusList()));
        return wrapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(OrderCreateDTO dto) {
        List<ProductSku> productSkus = productService.checkStock(dto.getSkuQuantityList());
        Order order = dto.buildEntity();
        String workNumber = dto.getWorkNumber();
        if (StringUtils.isNotBlank(workNumber)) {
            Work work = workService.getByWorkNumber(workNumber);
            if (work == null) {
                throw new BusinessException("任务不存在");
            }
            order.setWorkId(work.getId());
        }

        order.setWorkNumber(workNumber);

        save(order);

        List<SkuOrderDetailBO> skuOrderDetailBos = productService.packSkuOrderDetail(dto.getSkuQuantityList());
        List<OrderItem> orderItems = skuOrderDetailBos.stream().map(e -> e.buildOrderItem(order.getId())).toList();
        orderItemService.saveBatch(orderItems);
    }

    @Override
    public void update(OrderUpdateDTO dto) {
        Order order = BeanCopyUtils.copy(dto, Order.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create().eq(Order::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);
        saveOrUpdate(order);
    }

    @Override
    public PageResult<OrderVO> page(OrderListDTO dto) {
        Page<OrderVO> page = pageAs(PageUtils.getPage(dto), buildQueryWrapper(dto), OrderVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<OrderVO> list(OrderListDTO dto) {
        return listAs(buildQueryWrapper(dto), OrderVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public OrderVO detail(Long id) {
        OrderVO orderVO = mapper.selectOneWithRelationsByIdAs(id, OrderVO.class);
        return orderVO;
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<OrderImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), OrderImportDTO.class, true);
        List<OrderImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(OrderListDTO dto, HttpServletResponse response) {
        List<OrderVO> list = list(dto);
        String fileName = "物资订单模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "物资订单", OrderVO.class, os);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void review(OrderReviewDTO dto) {
        Order order = mapper.selectOneById(dto.getId());
        CommonResponseEnum.INVALID_ID.assertNull(order);

        OrderReviewStatus originStatus = order.status();
        if (OrderReviewStatus.Terminate.equals(originStatus) || OrderReviewStatus.InboundComplete.equals(originStatus)) {
            throw new BusinessException("流程已经终止或已经入库 无法再次审批");
        }

        OrderReviewStatus tagetStatus = OrderReviewStatus.matchIndex(originStatus.getIndex() + 1);
        BaseUserInfo userInfo = LoginUtils.getUserInfo();
        order.setReviewUserId(userInfo.getId());
        order.setReviewName(userInfo.getName());
        order.setReviewPhone(userInfo.getPhone());
        order.setReviewOpinion(dto.getReviewOpinion());
        order.addRemark(dto.getRemark());
        order.setStatus(tagetStatus.getCode());
        if (dto.notAgree()) {
            order.setStatus(OrderReviewStatus.Terminate.getCode());
        }
        saveOrUpdate(order);
        List<OrderItem> orderItems = orderItemService.listByOrderId(order.getId());
        List<SkuQuantityBO> skuQuantityBos = orderItems.stream().map(OrderItem::buildSkuQuantityBO).toList();
        if (OrderReviewStatus.OutboundApprove.equals(tagetStatus)) {
            productService.deductStock(skuQuantityBos);
        }
        if (OrderReviewStatus.InboundComplete.equals(tagetStatus)) {
            productService.addStock(skuQuantityBos);
        }

        OrderReview orderReview = new OrderReview();
        orderReview.setOrderId(order.getId());
        orderReview.setReviewUserId(userInfo.getId());
        orderReview.setReviewName(userInfo.getName());
        orderReview.setReviewPhone(userInfo.getPhone());
        orderReview.setOriginStatus(originStatus.getCode());
        orderReview.setTargetStatus(tagetStatus.getCode());
        orderReview.setReviewOpinion(dto.getReviewOpinion());
        orderReview.setCreateTime(LocalDateTime.now());
        orderReview.setUpdateTime(LocalDateTime.now());
        orderReviewService.save(orderReview);
    }
}