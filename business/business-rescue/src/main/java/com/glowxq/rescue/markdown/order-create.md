# 物资订单管理 新增物资订单
后端已经完善了物资订单新增接口，请自行完成前端代码

相关模块接口路径：/src/api/modules/rescue/order
相关数据结构路径：src/api/interface/rescue/order
相关页面路径：src/views/rescue/order
相关枚举路径：src/enums/rescue/order


## 请求接口
后端接口：POST /order/create
## 请求参数
```java
@Data
@Schema(description = "Order添加DTO")
public class OrderCreateDTO implements BaseDTO {

    @Schema(description = "任务编号")
    private String workNumber;

    @Schema(description = "类型")
    @InEnum(enumClass = OrderType.class)
    private String type;

    @Schema(description = "操作原因")
    private String reason;

    @Schema(description = "出入库")
    private Boolean operate;

    @Schema(description = "备注")
    private String remark;

    @Size(min = 1, max = 100, message = "最多只能选择100个规格")
    @Schema(description = "skuId")
    private List<SkuQuantityBO> skuQuantityList;

    @Override
    public Order buildEntity() {
        Order order = BeanCopyUtils.copy(this, Order.class);
        order.setWorkNumber(NumberUtils.generateNumber(NumberPrefixEnum.Order));
        BaseUserInfo userInfo = LoginUtils.getUserInfo();
        order.setApplyUserId(userInfo.getId());
        order.setApplyName(userInfo.getName());
        order.setApplyPhone(userInfo.getPhone());
        return order;
    }

    public List<Long> skuIds() {
        return skuQuantityList.stream().map(SkuQuantityBO::getSkuId).toList();
    }
}

@Data
@Schema(description = "Order添加DTO")
@NoArgsConstructor
@AllArgsConstructor
public class SkuQuantityBO implements BaseDTO {

    @Schema(description = "skuId")
    private Long skuId;

    @Schema(description = "数量")
    private Integer quantity;

    @Override
    public Order buildEntity() {
        return BeanCopyUtils.copy(this, Order.class);
    }
}
```