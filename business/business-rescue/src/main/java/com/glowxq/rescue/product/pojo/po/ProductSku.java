package com.glowxq.rescue.product.pojo.po;

import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.core.common.exception.common.BusinessException;
import com.glowxq.core.util.NumberUtils;
import com.glowxq.mysql.EntityChangeListener;
import com.glowxq.rescue.common.enums.NumberPrefixEnum;
import com.glowxq.rescue.product.pojo.dto.inside.ProductLotInsideDTO;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 物资规格
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Table(value = "product_sku", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "物资规格")
public class ProductSku implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description = "规格ID")
    private Long id;

    @Schema(description = "物资id")
    private Long productId;

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "规格名称")
    private String name;

    @Schema(description = "规格图片")
    private String image;

    @Schema(description = "库存")
    private Integer stock;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "启用")
    private Boolean enable;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Column(ignore = true)
    private List<ProductLotInsideDTO> productLots;

    public void deductStock(Integer quantity) {
        this.stock -= quantity;
        if (this.stock < 0) {
            throw new BusinessException("%s:库存不足".formatted(name));
        }
    }

    public void addStock(Integer quantity) {
        this.stock += quantity;
    }

    public List<ProductLot> buildProductLots(Long productId) {
        if (CollectionUtils.isEmpty(productLots)) {
            return List.of();
        }
        return productLots.stream().map(ProductLotInsideDTO::buildEntity).peek(e -> {
            e.setProductId(productId);
            e.setSkuId(id);
            e.setLotNumber(NumberUtils.generateNumber(NumberPrefixEnum.ProductLot));
        }).toList();
    }
}