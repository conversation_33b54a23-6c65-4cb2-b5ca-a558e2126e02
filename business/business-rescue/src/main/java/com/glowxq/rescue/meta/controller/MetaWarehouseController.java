package com.glowxq.rescue.meta.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.rescue.meta.pojo.dto.MetaWarehouseCreateDTO;
import com.glowxq.rescue.meta.pojo.dto.MetaWarehouseListDTO;
import com.glowxq.rescue.meta.pojo.dto.MetaWarehouseUpdateDTO;
import com.glowxq.rescue.meta.pojo.vo.MetaWarehouseVO;
import com.glowxq.rescue.meta.service.MetaWarehouseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * meta/仓库 Api
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Tag(name = "仓库")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class MetaWarehouseController extends BaseApi {

    private final MetaWarehouseService metaWarehouseService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "meta.warehouse.create")
    @PostMapping("/meta-warehouse/create")
    public ApiResult<Void> create(@RequestBody MetaWarehouseCreateDTO dto) {
        metaWarehouseService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "meta.warehouse.update")
    @PutMapping("/meta-warehouse/update")
    public ApiResult<Void> update(@RequestBody MetaWarehouseUpdateDTO dto) {
        metaWarehouseService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "meta.warehouse.remove")
    @DeleteMapping("/meta-warehouse/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        metaWarehouseService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "meta.warehouse.query_table")
    @GetMapping("/meta-warehouse/list")
    public ApiResult<PageResult<MetaWarehouseVO>> list(MetaWarehouseListDTO dto) {
        return ApiPageResult.success(metaWarehouseService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "meta.warehouse.query_table")
    @GetMapping("/meta-warehouse/detail")
    public ApiResult<MetaWarehouseVO> detail(@RequestParam Long id) {
        return ApiResult.success(metaWarehouseService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "meta.warehouse.import")
    @PostMapping("/meta-warehouse/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        metaWarehouseService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "meta.warehouse.export")
    @PostMapping("/meta-warehouse/export")
    public void exportExcel(@RequestBody MetaWarehouseListDTO dto, HttpServletResponse response) {
        metaWarehouseService.exportExcel(dto, response);
    }
}