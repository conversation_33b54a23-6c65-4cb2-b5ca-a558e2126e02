package com.glowxq.rescue.product.pojo.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseVO;
import com.glowxq.rescue.product.pojo.po.ProductSku;
import com.mybatisflex.annotation.RelationOneToMany;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * Product返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "Product返回vo")
public class ProductSelectVO implements BaseVO {

    @ExcelIgnore
    @Schema(description = "物资ID")
    private Long id;

    @ExcelProperty(value = "名称")
    @Schema(description = "名称")
    private String name;

    @ExcelProperty(value = "图片")
    @Schema(description = "图片")
    private String image;

    @ExcelProperty(value = "物资编号")
    @Schema(description = "物资编号")
    private String productNumber;

    @ExcelProperty(value = "启用")
    @Schema(description = "启用")
    private Boolean enable;

    @RelationOneToMany(selfField = "id", targetField = "productId", targetTable = "product_sku")
    private List<ProductSku> productSkus;
}