package com.glowxq.rescue.product.pojo.po;

import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.mysql.EntityChangeListener;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * <p>
 * 物资包
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Table(value = "product_bundle", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "物资包")
public class ProductBundle implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description = "规格ID")
    private Long id;

    @Schema(description = "物资id")
    private Long productId;

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "规格ID")
    private Long skuId;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "启用")
    private Boolean enable;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}