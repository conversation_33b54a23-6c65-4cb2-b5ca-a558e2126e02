package com.glowxq.rescue.work.pojo.po;

import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.mysql.EntityChangeListener;
import com.glowxq.rescue.work.enums.WorkStatus;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * <p>
 * 任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Table(value = "work", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "任务")
public class Work implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description = "任务编号")
    private Long id;

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "分类id")
    private Long categoryId;

    @Schema(description = "地址id")
    private Long regionId;

    @Schema(description = "任务编号")
    private String workNumber;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "详情")
    private String content;

    @Schema(description = "报名要求")
    private String applyRequire;

    @Schema(description = "负责人ID")
    private Long principalUserId;

    @Schema(description = "负责人")
    private String principalName;

    @Schema(description = "负责人联系方式")
    private String principalPhone;

    @Schema(description = "求助人")
    private String askName;

    @Schema(description = "求助人联系方式")
    private String askContact;

    @Schema(description = "省市区地址")
    private String address;

    @Schema(description = "详细地址")
    private String detailAddress;

    @Schema(description = "经度")
    private Integer longitude;

    @Schema(description = "纬度")
    private Integer latitude;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "启用")
    private Boolean enable;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "签到检查地址")
    private Boolean enableCheckAddress;

    @Schema(description = "签到密码校验")
    private Boolean enableCheckPassword;

    @Schema(description = "时间校验")
    private Boolean enableCheckTime;

    @Schema(description = "签到密码")
    private String signOnPassword;

    @Schema(description = "签退密码")
    private String signExitPassword;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除")
    private String delFlag;

    @Schema(description = "创建人ID")
    private Long createId;

    @Schema(description = "更新人ID")
    private Long updateId;

    public WorkStatus status() {
        return WorkStatus.matchCode(status);
    }
}