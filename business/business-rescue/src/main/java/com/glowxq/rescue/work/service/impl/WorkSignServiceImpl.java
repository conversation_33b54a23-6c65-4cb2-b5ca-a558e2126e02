package com.glowxq.rescue.work.service.impl;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.common.exception.common.BusinessException;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.PageUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.rescue.user.pojo.po.UserInfo;
import com.glowxq.rescue.user.service.UserInfoService;
import com.glowxq.rescue.work.enums.SignType;
import com.glowxq.rescue.work.enums.WorkStatus;
import com.glowxq.rescue.work.mapper.WorkSignMapper;
import com.glowxq.rescue.work.pojo.dto.*;
import com.glowxq.rescue.work.pojo.po.Work;
import com.glowxq.rescue.work.pojo.po.WorkSign;
import com.glowxq.rescue.work.pojo.vo.WorkSignVO;
import com.glowxq.rescue.work.service.WorkService;
import com.glowxq.rescue.work.service.WorkSignService;
import com.glowxq.security.core.util.LoginUtils;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.OutputStream;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * <p>
 * 任务签到 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
@RequiredArgsConstructor
public class WorkSignServiceImpl extends ServiceImpl<WorkSignMapper, WorkSign> implements WorkSignService {

    private final WorkService workService;

    private final UserInfoService userInfoService;

    private static QueryWrapper buildQueryWrapper(WorkSignListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(WorkSign.class);
        wrapper.eq(WorkSign::getTenantId, dto.getTenantId(), Utils.isNotNull(dto.getTenantId()));
        wrapper.eq(WorkSign::getDeptId, dto.getDeptId(), Utils.isNotNull(dto.getDeptId()));
        wrapper.eq(WorkSign::getWorkId, dto.getWorkId(), Utils.isNotNull(dto.getWorkId()));
        wrapper.eq(WorkSign::getUserId, dto.getUserId(), Utils.isNotNull(dto.getUserId()));
        wrapper.eq(WorkSign::getSignType, dto.getSignType(), Utils.isNotNull(dto.getSignType()));
        wrapper.like(WorkSign::getName, dto.getName(), Utils.isNotNull(dto.getName()));
        wrapper.between(WorkSign::getSignDate,
                dto.getSignDateStart(),
                dto.getSignDateEnd(),
                Utils.isNotNull(dto.getSignDateStart()) && Utils.isNotNull(dto.getSignDateEnd()));
        wrapper.between(WorkSign::getSignTime,
                dto.getSignTimeStart(),
                dto.getSignTimeEnd(),
                Utils.isNotNull(dto.getSignTimeStart()) && Utils.isNotNull(dto.getSignTimeEnd()));
        return wrapper;
    }

    @Override
    public void create(WorkSignCreateDTO dto) {
        WorkSign workSign = BeanCopyUtils.copy(dto, WorkSign.class);
        save(workSign);
    }

    @Override
    public void update(WorkSignUpdateDTO dto) {
        WorkSign workSign = BeanCopyUtils.copy(dto, WorkSign.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
                              .eq(WorkSign::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(workSign);
    }

    @Override
    public PageResult<WorkSignVO> page(WorkSignListDTO dto) {
        Page<WorkSignVO> page = pageAs(PageUtils.getPage(dto), buildQueryWrapper(dto), WorkSignVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<WorkSignVO> list(WorkSignListDTO dto) {
        return listAs(buildQueryWrapper(dto), WorkSignVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public WorkSignVO detail(Long id) {
        WorkSign workSign = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(workSign);
        return BeanCopyUtils.copy(workSign, WorkSignVO.class);
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<WorkSignImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), WorkSignImportDTO.class, true);
        List<WorkSignImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(WorkSignListDTO dto, HttpServletResponse response) {
        List<WorkSignVO> list = list(dto);
        String fileName = "任务签到模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "任务签到", WorkSignVO.class, os);
    }

    private static void checkPreSign(WorkSignDTO dto, WorkStatus status, Work work, SignType signType) {
        if (status.getIndex() < WorkStatus.ConfirmedCanApply.getIndex() || status.getIndex() >= WorkStatus.Terminate.getIndex()) {
            throw new BusinessException("任务状态不允许签到");
        }
        if (work.getEnableCheckTime()) {
            LocalDateTime startTime = work.getStartTime();
            LocalDateTime endTime = work.getEndTime();
            if (startTime.isAfter(LocalDateTime.now()) || endTime.isBefore(LocalDateTime.now())) {
                throw new BusinessException("任务时间未开始或已结束");
            }
        }
        if (work.getEnableCheckAddress()) {
            String longitude = dto.getLongitude();
            String latitude = dto.getLatitude();
            if (longitude == null || latitude == null) {
                throw new BusinessException("请提供经纬度");
            }
            // if (!GeoUtils.isInside(longitude, latitude, work.getLongitude(), work.getLatitude(), work.getDetailAddress())) {
            //     throw new BusinessException("经纬度不在任务地址内");
            // }
        }
        if (work.getEnableCheckPassword()) {
            if (signType == null) {
                throw new BusinessException("请提供签到/签退");
            }
            switch (signType) {
                case SignOn -> {
                    String signOnPassword = dto.getSignOnPassword();
                    if (signOnPassword == null) {
                        throw new BusinessException("请提供签到/签退密码");
                    }
                    if (!signOnPassword.equals(work.getSignOnPassword())) {
                        throw new BusinessException("签到/签退密码错误");
                    }
                }

                case SignExit -> {
                    String signExitPassword = dto.getSignExitPassword();
                    if (signExitPassword == null) {
                        throw new BusinessException("请提供签到/签退密码");
                    }

                    if (!signExitPassword.equals(work.getSignExitPassword())) {
                        throw new BusinessException("签到/签退密码错误");
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sign(WorkSignDTO dto) {
        Work work = workService.getById(dto.getWorkId());
        CommonResponseEnum.INVALID_ID.assertNull(work);
        WorkStatus status = work.status();
        SignType signType = dto.signType();
        Long userId = LoginUtils.getUserId();
        checkPreSign(dto, status, work, signType);
        WorkSign sign = dto.buildEntity();
        sign.setUserId(userId);
        sign.setSignType(signType.name());
        sign.setSignDate(LocalDate.now());
        sign.setSignTime(LocalDateTime.now());
        sign.setName(LoginUtils.getUserInfo().getName());
        switch (signType) {
            case SignOn -> {
                WorkSign workSign = mapper.getSign(dto.getWorkId(), userId, signType);
                if (workSign != null) {
                    throw new BusinessException("您已签到");
                }
                mapper.insert(sign, true);
            }
            case SignExit -> {
                WorkSign workSignOn = mapper.getSign(dto.getWorkId(), userId, SignType.SignOn);
                if (workSignOn == null) {
                    throw new BusinessException("请先签到");
                }
                WorkSign workSign = mapper.getSign(dto.getWorkId(), userId, signType);
                if (workSign != null) {
                    throw new BusinessException("您已签退");
                }
                mapper.insert(sign, true);
                LocalDateTime signOnTime = workSignOn.getSignTime();
                LocalDateTime signExitTime = sign.getSignTime();
                long duration = ChronoUnit.MINUTES.between(signOnTime, signExitTime);
                UserInfo userInfo = userInfoService.getById(userId);
                userInfo.addTotalDutyDuration((int) duration);
                userInfo.addYearDutyDuration((int) duration);
                userInfoService.updateById(userInfo);
            }
        }
    }
}