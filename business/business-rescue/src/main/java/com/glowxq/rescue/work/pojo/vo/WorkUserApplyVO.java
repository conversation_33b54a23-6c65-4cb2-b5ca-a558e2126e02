package com.glowxq.rescue.work.pojo.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseVO;
import com.glowxq.rescue.work.pojo.po.Work;
import com.mybatisflex.annotation.RelationOneToOne;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * WorkUserApply返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Schema(description = "WorkUserApply返回vo")
public class WorkUserApplyVO implements BaseVO {

    @ExcelIgnore
    @Schema(description = "id")
    private Long id;

    @ExcelProperty(value = "租户ID")
    @Schema(description = "租户ID")
    private String tenantId;

    @ExcelProperty(value = "部门ID")
    @Schema(description = "部门ID")
    private Long deptId;

    @ExcelProperty(value = "任务ID")
    @Schema(description = "任务ID")
    private Long workId;

    @ExcelProperty(value = "用户ID")
    @Schema(description = "用户ID")
    private Long userId;

    @ExcelProperty(value = "审批状态")
    @Schema(description = "审批状态")
    private String status;

    @ExcelProperty(value = "姓名")
    @Schema(description = "姓名")
    private String name;

    @RelationOneToOne(selfField = "workId", targetTable = "work", targetField = "id")
    private Work work;

    @Schema(description = "申请时间")
    private LocalDateTime applyTime;
}