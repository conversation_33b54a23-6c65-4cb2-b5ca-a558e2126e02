package com.glowxq.rescue.order.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.rescue.order.pojo.dto.OrderItemCreateDTO;
import com.glowxq.rescue.order.pojo.dto.OrderItemListDTO;
import com.glowxq.rescue.order.pojo.dto.OrderItemUpdateDTO;
import com.glowxq.rescue.order.pojo.vo.OrderItemVO;
import com.glowxq.rescue.order.service.OrderItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * order/订单子项 Api
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Tag(name = "订单子项")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class OrderItemController extends BaseApi {

    private final OrderItemService orderItemService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "order.item.create")
    @PostMapping("/order-item/create")
    public ApiResult<Void> create(@RequestBody OrderItemCreateDTO dto) {
        orderItemService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "order.item.update")
    @PutMapping("/order-item/update")
    public ApiResult<Void> update(@RequestBody OrderItemUpdateDTO dto) {
        orderItemService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "order.item.remove")
    @DeleteMapping("/order-item/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        orderItemService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "order.item.query_table")
    @GetMapping("/order-item/list")
    public ApiResult<PageResult<OrderItemVO>> list(OrderItemListDTO dto) {
        return ApiPageResult.success(orderItemService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "order.item.query_table")
    @GetMapping("/order-item/detail")
    public ApiResult<OrderItemVO> detail(@RequestParam Long id) {
        return ApiResult.success(orderItemService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "order.item.import")
    @PostMapping("/order-item/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        orderItemService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "order.item.export")
    @PostMapping("/order-item/export")
    public void exportExcel(@RequestBody OrderItemListDTO dto, HttpServletResponse response) {
        orderItemService.exportExcel(dto, response);
    }
}