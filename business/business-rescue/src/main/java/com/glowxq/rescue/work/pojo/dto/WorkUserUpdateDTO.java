package com.glowxq.rescue.work.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.work.pojo.po.WorkUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * WorkUser修改DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Schema(description = "WorkUser修改DTO")
public class WorkUserUpdateDTO implements BaseDTO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "任务ID")
    private Long workId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "姓名")
    private String name;

    @Override
    public WorkUser buildEntity() {
        return BeanCopyUtils.copy(this, WorkUser.class);
    }
}