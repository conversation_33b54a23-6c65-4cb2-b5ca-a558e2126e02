package com.glowxq.rescue.work.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.rescue.work.pojo.dto.WorkEventCreateDTO;
import com.glowxq.rescue.work.pojo.dto.WorkEventListDTO;
import com.glowxq.rescue.work.pojo.dto.WorkEventUpdateDTO;
import com.glowxq.rescue.work.pojo.vo.WorkEventVO;
import com.glowxq.rescue.work.service.WorkEventService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * work/任务事件 Api
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Tag(name = "任务事件")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class WorkEventController extends BaseApi {

    private final WorkEventService workEventService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "work.event.create")
    @PostMapping("/work-event/create")
    public ApiResult<Void> create(@RequestBody WorkEventCreateDTO dto) {
        workEventService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "work.event.update")
    @PutMapping("/work-event/update")
    public ApiResult<Void> update(@RequestBody WorkEventUpdateDTO dto) {
        workEventService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "work.event.remove")
    @DeleteMapping("/work-event/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        workEventService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "work.event.query_table")
    @GetMapping("/work-event/list")
    public ApiResult<PageResult<WorkEventVO>> list(WorkEventListDTO dto) {
        return ApiPageResult.success(workEventService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "work.event.query_table")
    @GetMapping("/work-event/detail")
    public ApiResult<WorkEventVO> detail(@RequestParam Long id) {
        return ApiResult.success(workEventService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "work.event.import")
    @PostMapping("/work-event/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        workEventService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "work.event.export")
    @PostMapping("/work-event/export")
    public void exportExcel(@RequestBody WorkEventListDTO dto, HttpServletResponse response) {
        workEventService.exportExcel(dto, response);
    }
}