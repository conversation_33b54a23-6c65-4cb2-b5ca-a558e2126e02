package com.glowxq.rescue.product.pojo.dto;

import com.glowxq.core.common.entity.PageQuery;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.product.pojo.po.Product;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * Product查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "Product查询DTO")
public class ProductListDTO extends PageQuery implements BaseDTO {

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "分类id")
    private Long categoryId;

    @Schema(description = "仓库id")
    private Long warehouseId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "图片")
    private String image;

    @Schema(description = "物资编号")
    private String productNumber;

    @Schema(description = "启用")
    private Boolean enable;

    @Schema(description = "标签ID")
    private List<Long> tagIds;

    @Override
    public Product buildEntity() {
        return BeanCopyUtils.copy(this, Product.class);
    }
}