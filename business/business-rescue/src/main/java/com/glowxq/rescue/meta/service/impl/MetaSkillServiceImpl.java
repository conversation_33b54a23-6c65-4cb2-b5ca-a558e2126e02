package com.glowxq.rescue.meta.service.impl;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.PageUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.rescue.meta.mapper.MetaSkillMapper;
import com.glowxq.rescue.meta.pojo.dto.MetaSkillCreateDTO;
import com.glowxq.rescue.meta.pojo.dto.MetaSkillImportDTO;
import com.glowxq.rescue.meta.pojo.dto.MetaSkillListDTO;
import com.glowxq.rescue.meta.pojo.dto.MetaSkillUpdateDTO;
import com.glowxq.rescue.meta.pojo.po.MetaSkill;
import com.glowxq.rescue.meta.pojo.vo.MetaSkillVO;
import com.glowxq.rescue.meta.service.MetaSkillService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 技能数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Service
@RequiredArgsConstructor
public class MetaSkillServiceImpl extends ServiceImpl<MetaSkillMapper, MetaSkill> implements MetaSkillService {

    @Override
    public void create(MetaSkillCreateDTO dto) {
        MetaSkill metaSkill = BeanCopyUtils.copy(dto, MetaSkill.class);
        save(metaSkill);
    }

    @Override
    public void update(MetaSkillUpdateDTO dto) {
        MetaSkill metaSkill = BeanCopyUtils.copy(dto, MetaSkill.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
                              .eq(MetaSkill::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(metaSkill);
    }

    @Override
    public PageResult<MetaSkillVO> page(MetaSkillListDTO dto) {
        Page<MetaSkillVO> page = pageAs(PageUtils.getPage(dto), buildQueryWrapper(dto), MetaSkillVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<MetaSkillVO> list(MetaSkillListDTO dto) {
        return listAs(buildQueryWrapper(dto), MetaSkillVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public MetaSkillVO detail(Long id) {
        MetaSkill metaSkill = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(metaSkill);
        return BeanCopyUtils.copy(metaSkill, MetaSkillVO.class);
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<MetaSkillImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), MetaSkillImportDTO.class, true);
        List<MetaSkillImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(MetaSkillListDTO dto, HttpServletResponse response) {
        List<MetaSkillVO> list = list(dto);
        String fileName = "技能数据模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "技能数据", MetaSkillVO.class, os);
    }

    private static QueryWrapper buildQueryWrapper(MetaSkillListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(MetaSkill.class);
        wrapper.eq(MetaSkill::getTenantId, dto.getTenantId(), Utils.isNotNull(dto.getTenantId()));
        wrapper.eq(MetaSkill::getDeptId, dto.getDeptId(), Utils.isNotNull(dto.getDeptId()));
        wrapper.eq(MetaSkill::getParentId, dto.getParentId(), Utils.isNotNull(dto.getParentId()));
        wrapper.eq(MetaSkill::getAncestors, dto.getAncestors(), Utils.isNotNull(dto.getAncestors()));
        wrapper.like(MetaSkill::getName, dto.getName(), Utils.isNotNull(dto.getName()));
        wrapper.eq(MetaSkill::getSort, dto.getSort(), Utils.isNotNull(dto.getSort()));
        wrapper.eq(MetaSkill::getImage, dto.getImage(), Utils.isNotNull(dto.getImage()));
        wrapper.eq(MetaSkill::getEnable, dto.getEnable(), Utils.isNotNull(dto.getEnable()));
        return wrapper;
    }
}