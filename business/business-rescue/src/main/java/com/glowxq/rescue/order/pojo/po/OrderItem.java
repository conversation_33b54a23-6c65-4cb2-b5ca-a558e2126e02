package com.glowxq.rescue.order.pojo.po;

import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.mysql.EntityChangeListener;
import com.glowxq.rescue.order.pojo.bo.SkuQuantityBO;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单子项
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Table(value = "order_item", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "订单子项")
public class OrderItem implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description = "详情ID")
    private Long id;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "物资id")
    private Long productId;

    @Schema(description = "物资名")
    private String productName;

    @Schema(description = "物资图片")
    private String image;

    @Schema(description = "规格名")
    private Long skuId;

    @Schema(description = "规格名")
    private String skuName;

    @Schema(description = "物资规格图片")
    private String skuImage;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "单价")
    private BigDecimal price;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    public SkuQuantityBO buildSkuQuantityBO(){
        return new SkuQuantityBO(this.skuId, this.quantity);
    }
}