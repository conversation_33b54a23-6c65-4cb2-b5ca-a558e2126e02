package com.glowxq.rescue.work.mapper;

import com.glowxq.rescue.work.pojo.po.Work;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;

/**
 * <p>
 * 任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface WorkMapper extends BaseMapper<Work> {

    default Work getByWorkNumber(String workNumber) {
        QueryWrapper qw = QueryWrapper.create();
        qw.eq(Work::getWorkNumber, workNumber);
        return selectOneByQuery(qw);
    }
}