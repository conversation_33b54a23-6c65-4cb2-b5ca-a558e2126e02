package com.glowxq.rescue.work.pojo.dto;

import com.glowxq.core.common.entity.PageQuery;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.work.pojo.po.WorkAskApply;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * WorkAskApply查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Schema(description = "WorkAskApply查询DTO")
public class WorkAskApplyListDTO extends PageQuery implements BaseDTO {

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "审批人id")
    private Long reviewUserId;

    @Schema(description = "地址id")
    private Long regionId;

    @Schema(description = "求助详情")
    private String askContent;

    @Schema(description = "求助人联系方式")
    private String askPhone;

    @Schema(description = "求助人姓名")
    private String askName;

    @Schema(description = "省市区地址")
    private String address;

    @Schema(description = "详细地址")
    private String detailAddress;

    @Schema(description = "经度")
    private Integer longitude;

    @Schema(description = "纬度")
    private Integer latitude;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "申请时间")
    private LocalDateTime applyTime;

    @Override
    public WorkAskApply buildEntity() {
        return BeanCopyUtils.copy(this, WorkAskApply.class);
    }
}