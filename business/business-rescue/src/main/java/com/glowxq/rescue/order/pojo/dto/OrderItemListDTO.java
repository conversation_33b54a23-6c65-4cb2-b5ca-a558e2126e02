package com.glowxq.rescue.order.pojo.dto;

import com.glowxq.core.common.entity.PageQuery;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.order.pojo.po.OrderItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * OrderItem查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "OrderItem查询DTO")
public class OrderItemListDTO extends PageQuery implements BaseDTO {

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "物资id")
    private Long productId;

    @Schema(description = "物资名")
    private String productName;

    @Schema(description = "物资图片")
    private String image;

    @Schema(description = "规格名")
    private Long skuId;

    @Schema(description = "规格名")
    private String skuName;

    @Schema(description = "物资规格图片")
    private String skuImage;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "单价")
    private BigDecimal price;

    @Override
    public OrderItem buildEntity() {
        return BeanCopyUtils.copy(this, OrderItem.class);
    }
}