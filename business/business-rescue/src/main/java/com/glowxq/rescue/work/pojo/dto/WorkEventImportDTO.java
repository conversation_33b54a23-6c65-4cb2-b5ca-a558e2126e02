package com.glowxq.rescue.work.pojo.dto;

import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.work.pojo.po.WorkEvent;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <p>
 * WorkEvent导入DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Schema(description = "WorkEvent导入DTO")
public class WorkEventImportDTO implements BaseDTO {

    @ExcelProperty(value = "任务ID")
    @Schema(description = "任务ID")
    private Long workId;

    @ExcelProperty(value = "租户ID")
    @Schema(description = "租户ID")
    private String tenantId;

    @ExcelProperty(value = "部门ID")
    @Schema(description = "部门ID")
    private Long deptId;

    @ExcelProperty(value = "记录人员ID")
    @Schema(description = "记录人员ID")
    private Long userId;

    @ExcelProperty(value = "事件标题")
    @Schema(description = "事件标题")
    private String title;

    @ExcelProperty(value = "事件内容")
    @Schema(description = "事件内容")
    private String content;

    @ExcelProperty(value = "事件图片")
    @Schema(description = "事件图片")
    private String image;

    @Schema(description = "事件时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventTime;

    @Override
    public WorkEvent buildEntity() {
        return BeanCopyUtils.copy(this, WorkEvent.class);
    }
}