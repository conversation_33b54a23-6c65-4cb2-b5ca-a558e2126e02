package com.glowxq.rescue.order.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.order.pojo.po.OrderReview;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * OrderReview添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "OrderReview添加DTO")
public class OrderReviewCreateDTO implements BaseDTO {

   @Schema(description =  "租户ID")
   private String tenantId;

   @Schema(description =  "部门ID")
   private Long deptId;

   @Schema(description =  "物资记录ID")
   private Long orderId;

   @Schema(description =  "审批用户ID")
   private Long reviewUserId;

   @Schema(description =  "审批人")
   private String reviewName;

   @Schema(description =  "审批人电话")
   private String reviewPhone;

   @Schema(description =  "原状态")
   private String originStatus;

   @Schema(description =  "审批后状态")
   private String targetStatus;

   @Schema(description =  "审批意见")
   private String reviewOpinion;

    @Override
    public OrderReview buildEntity() {
        return BeanCopyUtils.copy(this, OrderReview.class);
    }
}