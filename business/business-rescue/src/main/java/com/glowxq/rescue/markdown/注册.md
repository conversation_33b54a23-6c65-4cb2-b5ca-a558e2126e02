后端完成了注册接口的开发,根据后端的接口完成注册页面的开发，

```JAVA
    /**
     * 注册接口
     */
    @SaIgnore
    @PostMapping("/user-info/register")
    public ApiResult<Void> register(@RequestBody UserInfoRegisterDTO dto) {
        userInfoService.register(dto);
        return ApiResult.success();
    }

@Data
@Schema(description = "UserInfo添加DTO")
public class UserInfoRegisterDTO implements BaseDTO, UserDTO {

    @Schema(description = "队伍ID")
    private Long deptId;

    @Schema(description = "微信登录code")
    private String wechatCode;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "身份证号")
    private String identityCard;

    @Schema(description = "身份证有效期开始")
    private LocalDate identityStartDate;

    @Schema(description = "身份证有效期截止")
    private LocalDate identityEndDate;

    @Schema(description = "护照号")
    private String passportNumber;

    @Schema(description = "保险状态")
    private Boolean insuranceStatus;

    @Schema(description = "政治面貌")
    private String politicsStatus;

    @Schema(description = "血型")
    private String bloodType;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "生日")
    private LocalDate birthday;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "签名图片")
    private String signatureImage;

    @Schema(description = "身份证图片")
    private String identityImage;

    @Schema(description = "资料图片多个")
    private String informationImage;

    @Schema(description = "紧急联系人")
    private String emergencyContact;

    @Schema(description = "紧急联系人电话")
    private String emergencyContactPhone;

    @Schema(description = "医疗史")
    private String medicalHistory;

    @Schema(description = "过敏史")
    private String allergiesHistory;

    @Schema(description = "入队时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;
}
```

