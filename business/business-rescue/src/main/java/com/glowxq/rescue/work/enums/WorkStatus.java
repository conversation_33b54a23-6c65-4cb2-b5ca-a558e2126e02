package com.glowxq.rescue.work.enums;

import com.glowxq.core.common.enums.base.BaseType;
import com.glowxq.core.common.exception.common.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 物资订单审批状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/20
 */
@AllArgsConstructor
@Getter
public enum WorkStatus implements BaseType {
    None("None", 0, "无状态"),
    // 创建未确认
    CreateUnconfirmed("CreateUnconfirmed", 1, "创建未确认"),
    // 已确认可报名
    ConfirmedCanApply("ConfirmedCanApply", 2, "已确认可报名"),
    // 报名结束
    ApplyEnd("ApplyEnd", 3, "报名结束"),
    // 已结束
    Terminate("Terminate", 4, "已结束"),
    ;

    /**
     * code
     */
    private final String code;

    /**
     * 索引
     */
    private final Integer index;

    /**
     * 最近类型
     */
    private final String name;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static WorkStatus matchCode(String code) {
        for (WorkStatus orderReviewStatus : WorkStatus.values()) {
            if (orderReviewStatus.getCode().equals(code)) {
                return orderReviewStatus;
            }
        }
        return null;
    }

    /**
     * 根据index获取枚举
     *
     * @param index
     * @return
     */
    public static WorkStatus matchIndex(Integer index) {
        if (index == null || index < 0 || index > WorkStatus.Terminate.getIndex()) {
            throw new BusinessException("无效的[OrderReviewStatus]状态索引 index:" + index);
        }
        for (WorkStatus orderReviewStatus : WorkStatus.values()) {
            if (orderReviewStatus.getIndex().equals(index)) {
                return orderReviewStatus;
            }
        }
        throw new BusinessException("无效的[OrderReviewStatus]状态索引 index:" + index);
    }
}
