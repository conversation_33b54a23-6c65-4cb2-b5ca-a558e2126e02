package com.glowxq.rescue.work.pojo.po;

import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.mysql.EntityChangeListener;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * <p>
 * 任务事件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Table(value = "work_event", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "任务事件")
public class WorkEvent implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description = "事件id")
    private Long id;

    @Schema(description = "任务ID")
    private Long workId;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "记录人员ID")
    private Long userId;

    @Schema(description = "事件标题")
    private String title;

    @Schema(description = "事件内容")
    private String content;

    @Schema(description = "事件图片")
    private String image;

    @Schema(description = "事件时间")
    private LocalDateTime eventTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除")
    private String delFlag;

    @Schema(description = "创建人ID")
    private Long createId;

    @Schema(description = "更新人ID")
    private Long updateId;
}