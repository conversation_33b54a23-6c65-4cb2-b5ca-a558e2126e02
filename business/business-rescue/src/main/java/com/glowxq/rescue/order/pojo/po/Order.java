package com.glowxq.rescue.order.pojo.po;

import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.core.util.SpringUtils;
import com.glowxq.core.util.StringUtils;
import com.glowxq.mysql.EntityChangeListener;
import com.glowxq.rescue.order.enums.OrderReviewStatus;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 物资订单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Table(value = "order", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "物资订单")
public class Order implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description = "记录ID")
    private Long id;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "任务id")
    private Long workId;

    @Schema(description = "任务编号")
    private String workNumber;

    @Schema(description = "订单编号")
    private String orderNumber;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "操作原因")
    private String reason;

    @Schema(description = "申请用户ID")
    private Long applyUserId;

    @Schema(description = "申请用户名")
    private String applyName;

    @Schema(description = "申请用户手机号")
    private String applyPhone;

    @Schema(description = "出入库")
    private Boolean operate;

    @Schema(description = "审批用户ID")
    private Long reviewUserId;

    @Schema(description = "审批人")
    private String reviewName;

    @Schema(description = "审批人电话")
    private String reviewPhone;

    @Schema(description = "审批意见")
    private String reviewOpinion;

    @Schema(description = "总金额")
    private BigDecimal totalPrice;

    @Schema(description = "物资总数量")
    private Integer totalAmount;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    public void addRemark(String remark) {
        this.remark = this.remark == null ? remark : this.remark + "\n" + StringUtils.trim(remark);
    }

    public OrderReviewStatus status() {
        return OrderReviewStatus.matchCode(this.status);
    }
}