package com.glowxq.rescue.meta.pojo.po;

import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.mysql.EntityChangeListener;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * <p>
 * 仓库
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Table(value = "meta_warehouse", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "仓库")
public class MetaWarehouse implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description = "仓库id")
    private Long id;

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "地址id")
    private Long regionId;

    @Schema(description = "公共")
    private Boolean common;

    @Schema(description = "仓库名")
    private String name;

    @Schema(description = "仓库图片")
    private String image;

    @Schema(description = "仓库地址")
    private String address;

    @Schema(description = "详细地址")
    private String detailAddress;

    @Schema(description = "定位信息")
    private String location;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "启用仓库")
    private Boolean enable;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}