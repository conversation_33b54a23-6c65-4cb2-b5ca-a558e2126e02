package com.glowxq.rescue.meta.pojo.dto;

import com.glowxq.core.common.entity.PageQuery;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.meta.pojo.po.MetaWarehouse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * MetaWarehouse查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "MetaWarehouse查询DTO")
public class MetaWarehouseListDTO extends PageQuery implements BaseDTO {

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "地址id")
    private Long regionId;

    @Schema(description = "公共")
    private Boolean common;

    @Schema(description = "仓库名")
    private String name;

    @Schema(description = "仓库图片")
    private String image;

    @Schema(description = "仓库地址")
    private String address;

    @Schema(description = "详细地址")
    private String detailAddress;

    @Schema(description = "定位信息")
    private String location;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "启用仓库")
    private Boolean enable;

    @Override
    public MetaWarehouse buildEntity() {
        return BeanCopyUtils.copy(this, MetaWarehouse.class);
    }
}