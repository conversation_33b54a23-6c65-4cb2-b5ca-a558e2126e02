package com.glowxq.rescue.product.service.impl;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.PageUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.rescue.product.mapper.ProductBundleMapper;
import com.glowxq.rescue.product.pojo.dto.ProductBundleCreateDTO;
import com.glowxq.rescue.product.pojo.dto.ProductBundleImportDTO;
import com.glowxq.rescue.product.pojo.dto.ProductBundleListDTO;
import com.glowxq.rescue.product.pojo.dto.ProductBundleUpdateDTO;
import com.glowxq.rescue.product.pojo.po.ProductBundle;
import com.glowxq.rescue.product.pojo.vo.ProductBundleVO;
import com.glowxq.rescue.product.service.ProductBundleService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 物资包 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Service
@RequiredArgsConstructor
public class ProductBundleServiceImpl extends ServiceImpl<ProductBundleMapper, ProductBundle> implements ProductBundleService {

    @Override
    public void create(ProductBundleCreateDTO dto) {
        ProductBundle productBundle = BeanCopyUtils.copy(dto, ProductBundle.class);
        save(productBundle);
    }

    @Override
    public void update(ProductBundleUpdateDTO dto) {
        ProductBundle productBundle = BeanCopyUtils.copy(dto, ProductBundle.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
                              .eq(ProductBundle::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(productBundle);
    }

    @Override
    public PageResult<ProductBundleVO> page(ProductBundleListDTO dto) {
        Page<ProductBundleVO> page = pageAs(PageUtils.getPage(dto), buildQueryWrapper(dto), ProductBundleVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<ProductBundleVO> list(ProductBundleListDTO dto) {
        return listAs(buildQueryWrapper(dto), ProductBundleVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public ProductBundleVO detail(Long id) {
        ProductBundle productBundle = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(productBundle);
        return BeanCopyUtils.copy(productBundle, ProductBundleVO.class);
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<ProductBundleImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), ProductBundleImportDTO.class, true);
        List<ProductBundleImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(ProductBundleListDTO dto, HttpServletResponse response) {
        List<ProductBundleVO> list = list(dto);
        String fileName = "物资包模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "物资包", ProductBundleVO.class, os);
    }

    private static QueryWrapper buildQueryWrapper(ProductBundleListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(ProductBundle.class);
        wrapper.eq(ProductBundle::getProductId, dto.getProductId(), Utils.isNotNull(dto.getProductId()));
        wrapper.eq(ProductBundle::getTenantId, dto.getTenantId(), Utils.isNotNull(dto.getTenantId()));
        wrapper.eq(ProductBundle::getDeptId, dto.getDeptId(), Utils.isNotNull(dto.getDeptId()));
        wrapper.eq(ProductBundle::getSkuId, dto.getSkuId(), Utils.isNotNull(dto.getSkuId()));
        wrapper.eq(ProductBundle::getQuantity, dto.getQuantity(), Utils.isNotNull(dto.getQuantity()));
        wrapper.eq(ProductBundle::getEnable, dto.getEnable(), Utils.isNotNull(dto.getEnable()));
        return wrapper;
    }
}