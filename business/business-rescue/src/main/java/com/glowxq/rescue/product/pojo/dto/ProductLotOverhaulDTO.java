package com.glowxq.rescue.product.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.product.pojo.po.ProductLot;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * ProductLot 检修 DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "ProductLot修改DTO")
public class ProductLotOverhaulDTO implements BaseDTO {

    @Schema(description = "批次ID")
    @NotNull(message = "批次ID不能为空")
    @Size(min = 1, message = "批次ID不能为空")
    private List<Long> lotIds;

    /**
     * 下次检修时间,不传则默认 当前时间+检修间隔时间
     */
    @Schema(description = "下次检修时间")
    private LocalDate overhaulNextDate;

    /**
     * 检修日期,不传则默认 当前时间
     */
    @Schema(description = "检修日期")
    private LocalDate overhaulDate;

    @Override
    public ProductLot buildEntity() {
        return BeanCopyUtils.copy(this, ProductLot.class);
    }

    public LocalDate overhaulNextDate(Integer overhaulGap) {
        return overhaulNextDate == null ? overhaulDate().plusDays(overhaulGap) : overhaulNextDate;
    }

    public LocalDate overhaulDate() {
        return overhaulDate == null ? LocalDate.now() : overhaulDate;
    }
}