package com.glowxq.rescue.work.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.work.pojo.dto.inside.WorkEventInsideCreateDTO;
import com.glowxq.rescue.work.pojo.po.Work;
import com.glowxq.rescue.work.pojo.po.WorkEvent;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * Work添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Schema(description = "Work添加DTO")
public class WorkCreateDTO implements BaseDTO {

   @Schema(description = "租户id")
   private String tenantId;

   @Schema(description = "部门ID")
   private Long deptId;

   @Schema(description = "分类id")
   private Long categoryId;

   @Schema(description = "地址id")
   private Long regionId;

   @Schema(description = "任务编号")
   private String workNumber;

   @Schema(description = "标题")
   private String title;

   @Schema(description = "详情")
   private String content;

   @Schema(description = "报名要求")
   private String applyRequire;

   @Schema(description = "负责人ID")
   private Long principalUserId;

   @Schema(description = "负责人")
   private String principalName;

   @Schema(description = "负责人联系方式")
   private String principalPhone;

   @Schema(description = "求助人")
   private String askName;

   @Schema(description = "求助人联系方式")
   private String askContact;

   @Schema(description = "省市区地址")
   private String address;

   @Schema(description = "详细地址")
   private String detailAddress;

   @Schema(description = "经度")
   private Integer longitude;

   @Schema(description = "纬度")
   private Integer latitude;

   @Schema(description = "备注")
   private String remark;

   @Schema(description = "启用")
   private Boolean enable;

   @Schema(description = "状态")
   private String status;

   @Schema(description = "签到检查地址")
   private Boolean enableCheckAddress;

   @Schema(description = "签到密码校验")
   private Boolean enableCheckPassword;

   @Schema(description = "时间校验")
   private Boolean enableCheckTime;

   @Schema(description = "签到密码")
   private String signOnPassword;

   @Schema(description = "签退密码")
   private String signExitPassword;

   @Schema(description = "开始时间")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private LocalDateTime startTime;

   @Schema(description = "结束时间")
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private LocalDateTime endTime;

   private List<Long> userIds;

   private List<WorkEventInsideCreateDTO> events;

   @Override
   public Work buildEntity() {
        return BeanCopyUtils.copy(this, Work.class);
    }

   public List<WorkEvent> buildEvents(Long workId, Long userId) {
      return events.stream().map(WorkEventInsideCreateDTO::buildEntity)
                   .peek(e -> {
                      e.setWorkId(workId);
                      e.setUserId(userId);
                   })
                   .toList();
   }
}