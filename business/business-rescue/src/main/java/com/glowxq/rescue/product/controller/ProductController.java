package com.glowxq.rescue.product.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.rescue.product.pojo.dto.ProductCreateDTO;
import com.glowxq.rescue.product.pojo.dto.ProductListDTO;
import com.glowxq.rescue.product.pojo.dto.ProductUpdateDTO;
import com.glowxq.rescue.product.pojo.vo.ProductSelectVO;
import com.glowxq.rescue.product.pojo.vo.ProductVO;
import com.glowxq.rescue.product.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * product/物资品类 Api
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Tag(name = "物资品类")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class ProductController extends BaseApi {

    private final ProductService productService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "product.create")
    @PostMapping("/product/create")
    public ApiResult<Void> create(@RequestBody ProductCreateDTO dto) {
        productService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "product.update")
    @PutMapping("/product/update")
    public ApiResult<Void> update(@RequestBody ProductUpdateDTO dto) {
        productService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "product.remove")
    @DeleteMapping("/product/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        productService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "product.query_table")
    @PostMapping("/product/list")
    public ApiResult<PageResult<ProductVO>> list(@RequestBody ProductListDTO dto) {
        return ApiPageResult.success(productService.page(dto));
    }

    @Operation(summary = "选择器查询")
    @SaCheckPermission(value = "product.query_table")
    @PostMapping("/product/select")
    public ApiResult<PageResult<ProductSelectVO>> pageSelect(@RequestBody ProductListDTO dto) {
        return ApiPageResult.success(productService.pageSelect(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "product.query_table")
    @GetMapping("/product/detail")
    public ApiResult<ProductVO> detail(@RequestParam Long id) {
        return ApiResult.success(productService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "product.import")
    @PostMapping("/product/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        productService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "product.export")
    @PostMapping("/product/export")
    public void exportExcel(@RequestBody ProductListDTO dto, HttpServletResponse response) {
        productService.exportExcel(dto, response);
    }
}