package com.glowxq.rescue.product.pojo.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseVO;
import com.glowxq.system.meta.pojo.po.MetaTag;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.RelationOneToOne;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * Product返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "Product返回vo")
public class ProductVO implements BaseVO {

    @ExcelIgnore
    @Schema(description = "物资ID")
    private Long id;

    @ExcelProperty(value = "租户id")
    @Schema(description = "租户id")
    private String tenantId;

    @ExcelProperty(value = "部门id")
    @Schema(description = "部门id")
    private Long deptId;

    @ExcelProperty(value = "分类id")
    @Schema(description = "分类id")
    private Long categoryId;

    @RelationOneToOne(selfField = "categoryId", targetField = "id", targetTable = "meta_category", valueField = "name")
    @ExcelProperty(value = "分类名")
    @Schema(description = "分类名")
    private String categoryName;

    @ExcelProperty(value = "仓库id")
    @Schema(description = "仓库id")
    private Long warehouseId;

    @RelationOneToOne(selfField = "warehouseId", targetField = "id", targetTable = "meta_warehouse", valueField = "name")
    @ExcelProperty(value = "仓库名")
    @Schema(description = "仓库名")
    private String warehouseName;

    @ExcelProperty(value = "名称")
    @Schema(description = "名称")
    private String name;

    @ExcelProperty(value = "图片")
    @Schema(description = "图片")
    private String image;

    @ExcelProperty(value = "物资编号")
    @Schema(description = "物资编号")
    private String productNumber;

    @ExcelProperty(value = "启用")
    @Schema(description = "启用")
    private Boolean enable;

    @RelationOneToMany(selfField = "id", targetField = "productId", targetTable = "product_sku")
    private List<ProductSkuVO> productSkus;

    @Schema(description = "标签数据")
    private List<MetaTag> tags;
}