package com.glowxq.rescue.user.enums;

import com.glowxq.core.common.enums.base.BaseType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 血型
 *
 * <AUTHOR>
 * @date 2025/06/21
 */
@AllArgsConstructor
@Getter
public enum BloodType implements BaseType {
    A("A", "A型"),
    B("B", "B型"),
    O("O", "O型"),
    AB("AB", "AB型"),
    Unknown("Unknown", "未知");;

    /**
     * code
     */
    private final String code;

    /**
     * 最近类型
     */
    private final String name;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static BloodType matchCode(String code) {
        for (BloodType type : BloodType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
