package com.glowxq.rescue.order.pojo.dto;

import com.glowxq.core.common.entity.PageQuery;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.order.pojo.po.Order;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Order查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Schema(description = "Order查询DTO")
public class OrderListDTO extends PageQuery implements BaseDTO {

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "任务id")
    private Long workId;

    @Schema(description = "任务编号")
    private String workNumber;

    @Schema(description = "订单编号")
    private String orderNumber;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "操作原因")
    private String reason;

    @Schema(description = "申请用户ID")
    private Long applyUserId;

    @Schema(description = "申请用户名")
    private String applyName;

    @Schema(description = "申请用户手机号")
    private String applyPhone;

    @Schema(description = "出入库")
    private Boolean operate;

    @Schema(description = "审批用户ID")
    private Long reviewUserId;

    @Schema(description = "审批人")
    private String reviewName;

    @Schema(description = "审批人电话")
    private String reviewPhone;

    @Schema(description = "审批意见")
    private String reviewOpinion;

    @Schema(description = "总金额")
    private BigDecimal totalPrice;

    @Schema(description = "物资总数量")
    private Integer totalAmount;

    @Schema(description = "筛选我的申请")
    private Boolean myApply;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "状态")
    private List<String> statusList;


    @Override
    public Order buildEntity() {
        return BeanCopyUtils.copy(this, Order.class);
    }
}