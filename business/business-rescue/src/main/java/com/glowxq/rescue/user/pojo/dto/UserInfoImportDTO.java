package com.glowxq.rescue.user.pojo.dto;

import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.user.pojo.po.UserInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * UserInfo导入DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Schema(description = "UserInfo导入DTO")
public class UserInfoImportDTO implements BaseDTO {

    @ExcelProperty(value = "用户ID")
    @Schema(description = "用户ID")
    private Long userId;

    @ExcelProperty(value = "队伍ID")
    @Schema(description = "队伍ID")
    private Long deptId;

    @ExcelProperty(value = "姓名")
    @Schema(description = "姓名")
    private String name;

    @ExcelProperty(value = "昵称")
    @Schema(description = "昵称")
    private String nickName;

    @ExcelProperty(value = "编号前缀")
    @Schema(description = "编号前缀")
    private String numberPrefix;

    @ExcelProperty(value = "编号")
    @Schema(description = "编号")
    private Integer number;

    @ExcelProperty(value = "职务")
    @Schema(description = "职务")
    private String post;

    @ExcelProperty(value = "头像")
    @Schema(description = "头像")
    private String avatar;

    @ExcelProperty(value = "联系电话")
    @Schema(description = "联系电话")
    private String phone;

    @ExcelProperty(value = "身份证号")
    @Schema(description = "身份证号")
    private String identityCard;

    @ExcelProperty(value = "身份证有效期开始")
    @Schema(description = "身份证有效期开始")
    private LocalDate identityStartDate;

    @ExcelProperty(value = "身份证有效期截止")
    @Schema(description = "身份证有效期截止")
    private LocalDate identityEndDate;

    @ExcelProperty(value = "护照号")
    @Schema(description = "护照号")
    private String passportNumber;

    @ExcelProperty(value = "保险状态")
    @Schema(description = "保险状态")
    private Boolean insuranceStatus;

    @ExcelProperty(value = "政治面貌")
    @Schema(description = "政治面貌")
    private String politicsStatus;

    @ExcelProperty(value = "血型")
    @Schema(description = "血型")
    private String bloodType;

    @ExcelProperty(value = "性别")
    @Schema(description = "性别")
    private String sex;

    @ExcelProperty(value = "生日")
    @Schema(description = "生日")
    private LocalDate birthday;

    @ExcelProperty(value = "备注")
    @Schema(description = "备注")
    private String remark;

    @ExcelProperty(value = "签名图片")
    @Schema(description = "签名图片")
    private String signatureImage;

    @ExcelProperty(value = "身份证图片")
    @Schema(description = "身份证图片")
    private String identityImage;

    @ExcelProperty(value = "资料图片多个")
    @Schema(description = "资料图片多个")
    private String informationImage;

    @ExcelProperty(value = "出勤总时长")
    @Schema(description = "出勤总时长")
    private Integer totalDutyDuration;

    @ExcelProperty(value = "年度出勤时长")
    @Schema(description = "年度出勤时长")
    private Integer yearDutyDuration;

    @ExcelProperty(value = "紧急联系人")
    @Schema(description = "紧急联系人")
    private String emergencyContact;

    @ExcelProperty(value = "紧急联系人电话")
    @Schema(description = "紧急联系人电话")
    private String emergencyContactPhone;

    @ExcelProperty(value = "医疗史")
    @Schema(description = "医疗史")
    private String medicalHistory;

    @ExcelProperty(value = "过敏史")
    @Schema(description = "过敏史")
    private String allergiesHistory;

    @ExcelProperty(value = "启用")
    @Schema(description = "启用")
    private Boolean enable;

    @Schema(description = "入队时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;

    @ExcelProperty(value = "租户ID")
    @Schema(description = "租户ID")
    private String tenantId;

    @Override
    public UserInfo buildEntity() {
        return BeanCopyUtils.copy(this, UserInfo.class);
    }
}