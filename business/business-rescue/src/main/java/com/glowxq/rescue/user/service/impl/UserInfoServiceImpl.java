package com.glowxq.rescue.user.service.impl;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.util.*;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.rescue.user.mapper.UserInfoMapper;
import com.glowxq.rescue.user.pojo.dto.*;
import com.glowxq.rescue.user.pojo.po.UserInfo;
import com.glowxq.rescue.user.pojo.vo.UserInfoVO;
import com.glowxq.rescue.user.service.UserInfoService;
import com.glowxq.security.core.constants.RoleConstant;
import com.glowxq.system.admin.pojo.dto.sysmenu.SysUserRoleDTO;
import com.glowxq.system.admin.pojo.dto.sysuser.UserDeptDTO;
import com.glowxq.system.admin.pojo.po.SysRole;
import com.glowxq.system.admin.pojo.po.SysUser;
import com.glowxq.system.admin.service.SysRoleService;
import com.glowxq.system.admin.service.SysUserService;
import com.glowxq.wechat.applet.WechatService;
import com.glowxq.wechat.applet.pojo.LoginInfoResult;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.OutputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p>
 * 用户信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Service
@RequiredArgsConstructor
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

    private final SysUserService sysUserService;

    private final WechatService wechatService;

    private final SysRoleService roleService;

    private static QueryWrapper buildQueryWrapper(UserInfoListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(UserInfo.class);
        wrapper.eq(UserInfo::getUserId, dto.getUserId(), Utils.isNotNull(dto.getUserId()));
        wrapper.eq(UserInfo::getDeptId, dto.getDeptId(), Utils.isNotNull(dto.getDeptId()));
        wrapper.like(UserInfo::getName, dto.getName(), Utils.isNotNull(dto.getName()));
        wrapper.like(UserInfo::getNickName, dto.getNickName(), Utils.isNotNull(dto.getNickName()));
        wrapper.eq(UserInfo::getNumberPrefix, dto.getNumberPrefix(), Utils.isNotNull(dto.getNumberPrefix()));
        wrapper.eq(UserInfo::getNumber, dto.getNumber(), Utils.isNotNull(dto.getNumber()));
        wrapper.eq(UserInfo::getPost, dto.getPost(), Utils.isNotNull(dto.getPost()));
        wrapper.eq(UserInfo::getAvatar, dto.getAvatar(), Utils.isNotNull(dto.getAvatar()));
        wrapper.eq(UserInfo::getPhone, dto.getPhone(), Utils.isNotNull(dto.getPhone()));
        wrapper.eq(UserInfo::getIdentityCard, dto.getIdentityCard(), Utils.isNotNull(dto.getIdentityCard()));
        wrapper.between(UserInfo::getIdentityStartDate,
                dto.getIdentityStartDateStart(),
                dto.getIdentityStartDateEnd(),
                Utils.isNotNull(dto.getIdentityStartDateStart()) && Utils.isNotNull(dto.getIdentityStartDateEnd()));
        wrapper.between(UserInfo::getIdentityEndDate,
                dto.getIdentityEndDateStart(),
                dto.getIdentityEndDateEnd(),
                Utils.isNotNull(dto.getIdentityEndDateStart()) && Utils.isNotNull(dto.getIdentityEndDateEnd()));
        wrapper.eq(UserInfo::getPassportNumber, dto.getPassportNumber(), Utils.isNotNull(dto.getPassportNumber()));
        wrapper.eq(UserInfo::getInsuranceStatus, dto.getInsuranceStatus(), Utils.isNotNull(dto.getInsuranceStatus()));
        wrapper.eq(UserInfo::getPoliticsStatus, dto.getPoliticsStatus(), Utils.isNotNull(dto.getPoliticsStatus()));
        wrapper.eq(UserInfo::getBloodType, dto.getBloodType(), Utils.isNotNull(dto.getBloodType()));
        wrapper.eq(UserInfo::getSex, dto.getSex(), Utils.isNotNull(dto.getSex()));
        wrapper.between(UserInfo::getBirthday,
                dto.getBirthdayStart(),
                dto.getBirthdayEnd(),
                Utils.isNotNull(dto.getBirthdayStart()) && Utils.isNotNull(dto.getBirthdayEnd()));
        wrapper.eq(UserInfo::getSignatureImage, dto.getSignatureImage(), Utils.isNotNull(dto.getSignatureImage()));
        wrapper.eq(UserInfo::getIdentityImage, dto.getIdentityImage(), Utils.isNotNull(dto.getIdentityImage()));
        wrapper.eq(UserInfo::getInformationImage, dto.getInformationImage(), Utils.isNotNull(dto.getInformationImage()));
        wrapper.eq(UserInfo::getTotalDutyDuration, dto.getTotalDutyDuration(), Utils.isNotNull(dto.getTotalDutyDuration()));
        wrapper.eq(UserInfo::getYearDutyDuration, dto.getYearDutyDuration(), Utils.isNotNull(dto.getYearDutyDuration()));
        wrapper.eq(UserInfo::getEmergencyContact, dto.getEmergencyContact(), Utils.isNotNull(dto.getEmergencyContact()));
        wrapper.eq(UserInfo::getEmergencyContactPhone, dto.getEmergencyContactPhone(), Utils.isNotNull(dto.getEmergencyContactPhone()));
        wrapper.eq(UserInfo::getMedicalHistory, dto.getMedicalHistory(), Utils.isNotNull(dto.getMedicalHistory()));
        wrapper.eq(UserInfo::getAllergiesHistory, dto.getAllergiesHistory(), Utils.isNotNull(dto.getAllergiesHistory()));
        wrapper.eq(UserInfo::getEnable, dto.getEnable(), Utils.isNotNull(dto.getEnable()));
        wrapper.between(UserInfo::getApproveTime,
                dto.getApproveTimeStart(),
                dto.getApproveTimeEnd(),
                Utils.isNotNull(dto.getApproveTimeStart()) && Utils.isNotNull(dto.getApproveTimeEnd()));
        return wrapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(UserInfoCreateDTO dto) {
        UserInfo userInfo = BeanCopyUtils.copy(dto, UserInfo.class);
        SysUser sysUser = sysUserService.create(dto.buildSysUserDTO());
        sysUserService.bindUserDept(new UserDeptDTO(sysUser.getId(), dto.getDeptId()));
        sysUserService.changeSysUserRole(new SysUserRoleDTO(sysUser.getId(), dto.getRoleIds()));
        Integer maxNumber = Optional.ofNullable(mapper.getMaxByDeptNumber(dto.getDeptId())).map(i -> ++i).orElse(1);
        userInfo.setId(sysUser.getId());
        userInfo.setUserId(sysUser.getId());
        userInfo.setNumberPrefix(dto.getNumberPrefix());
        userInfo.setNumber(maxNumber);
        save(userInfo);
    }

    @Override
    public void update(UserInfoUpdateDTO dto) {
        UserInfo userInfo = BeanCopyUtils.copy(dto, UserInfo.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create().eq(UserInfo::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);
        saveOrUpdate(userInfo);
        sysUserService.update(dto.buildSysUserDTO());
        sysUserService.bindUserDept(new UserDeptDTO(dto.getId(), dto.getDeptId()));
        sysUserService.changeSysUserRole(new SysUserRoleDTO(dto.getId(), dto.getRoleIds()));
    }

    @Override
    public PageResult<UserInfoVO> page(UserInfoListDTO dto) {
        Page<UserInfoVO> page = mapper.paginateWithRelationsAs(PageUtils.getPage(dto), buildQueryWrapper(dto), UserInfoVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<UserInfoVO> list(UserInfoListDTO dto) {
        return mapper.selectListWithRelationsByQueryAs(buildQueryWrapper(dto), UserInfoVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
        sysUserService.remove(dto);
    }

    @Override
    public UserInfoVO detail(Long id) {
        CommonResponseEnum.INVALID_ID.assertNull(id);
        List<SysRole> roles = roleService.listByUserId(id);
        UserInfoVO userInfoVo = mapper.selectOneWithRelationsByIdAs(id, UserInfoVO.class);
        userInfoVo.setRoles(roles);
        return userInfoVo;
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<UserInfoImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), UserInfoImportDTO.class, true);
        List<UserInfoImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(UserInfoListDTO dto, HttpServletResponse response) {
        List<UserInfoVO> list = list(dto);
        String fileName = "用户信息模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "用户信息", UserInfoVO.class, os);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindUserDept(UserDeptDTO dto) {
        sysUserService.bindUserDept(dto);
        Long deptId = dto.getDeptIds().getFirst();
        List<UserInfo> userInfos = mapper.selectListByIds(dto.getUserIds());
        List<UserInfo> userInfoList = userInfos.stream().peek(userInfo -> userInfo.setDeptId(deptId)).toList();
        updateBatch(userInfoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void register(UserInfoRegisterDTO dto) {
        UserInfoCreateDTO userInfoCreateDto = BeanCopyUtils.copy(dto, UserInfoCreateDTO.class);
        SysRole role = roleService.getByPerm(RoleConstant.MEMBER);
        String wechatCode = dto.getWechatCode();
        if (StringUtils.isNotBlank(wechatCode)) {
            LoginInfoResult login = wechatService.login(wechatCode);
            userInfoCreateDto.setOpenid(login.getOpenid());
        }
        userInfoCreateDto.setRoleIds(List.of(role.getId()));
        this.create(userInfoCreateDto);
    }

    @PostConstruct
    @Transactional(rollbackFor = Exception.class)
    public void initUserInfo() {
        if (sysUserService.count() == this.count()) {
            return;
        }
        List<SysUser> sysUserList = sysUserService.list();

        AtomicInteger index = new AtomicInteger();
        List<UserInfo> userInfoList = sysUserList.stream().map(sysUser -> {
            UserInfo userInfo = new UserInfo();
            userInfo.setId(sysUser.getId());
            userInfo.setUserId(sysUser.getId());
            userInfo.setDeptId(-1L);
            userInfo.setName(StringUtils.defaultIfBlank(sysUser.getName(), ""));
            userInfo.setNickName(StringUtils.defaultIfBlank(sysUser.getNickname(), ""));
            userInfo.setNumberPrefix("");
            userInfo.setNumber(index.getAndIncrement());
            userInfo.setPost("");
            userInfo.setAvatar(StringUtils.defaultIfBlank(sysUser.getLogo(), ""));
            userInfo.setPhone(StringUtils.defaultIfBlank(sysUser.getPhone(), ""));
            userInfo.setIdentityCard(StringUtils.defaultIfBlank(sysUser.getIdCard(), ""));
            userInfo.setIdentityStartDate(LocalDate.now());
            userInfo.setIdentityEndDate(LocalDate.now());
            userInfo.setPassportNumber("");
            userInfo.setInsuranceStatus(false);
            userInfo.setPoliticsStatus("");
            userInfo.setBloodType("");
            userInfo.setSex("");
            userInfo.setBirthday(LocalDate.now());
            userInfo.setRemark("");
            userInfo.setSignatureImage("");
            userInfo.setIdentityImage("");
            userInfo.setInformationImage("");
            userInfo.setTotalDutyDuration(0);
            userInfo.setYearDutyDuration(0);
            userInfo.setEmergencyContact("");
            userInfo.setEmergencyContactPhone("");
            userInfo.setMedicalHistory("");
            userInfo.setAllergiesHistory("");
            userInfo.setEnable(true);
            userInfo.setApproveTime(LocalDateTime.now());
            userInfo.setCreateTime(LocalDateTime.now());
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.setDelFlag("F");
            userInfo.setCreateId(0L);
            userInfo.setUpdateId(0L);
            userInfo.setTenantId("");

            return userInfo;
        }).toList();
        saveBatch(userInfoList);
    }
}