package com.glowxq.rescue.product.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.rescue.product.pojo.dto.ProductBundleCreateDTO;
import com.glowxq.rescue.product.pojo.dto.ProductBundleListDTO;
import com.glowxq.rescue.product.pojo.dto.ProductBundleUpdateDTO;
import com.glowxq.rescue.product.pojo.vo.ProductBundleVO;
import com.glowxq.rescue.product.service.ProductBundleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * product/物资包 Api
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Tag(name = "物资包")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class ProductBundleController extends BaseApi {

    private final ProductBundleService productBundleService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "product.bundle.create")
    @PostMapping("/product-bundle/create")
    public ApiResult<Void> create(@RequestBody ProductBundleCreateDTO dto) {
        productBundleService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "product.bundle.update")
    @PutMapping("/product-bundle/update")
    public ApiResult<Void> update(@RequestBody ProductBundleUpdateDTO dto) {
        productBundleService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "product.bundle.remove")
    @DeleteMapping("/product-bundle/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        productBundleService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "product.bundle.query_table")
    @GetMapping("/product-bundle/list")
    public ApiResult<PageResult<ProductBundleVO>> list(ProductBundleListDTO dto) {
        return ApiPageResult.success(productBundleService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "product.bundle.query_table")
    @GetMapping("/product-bundle/detail")
    public ApiResult<ProductBundleVO> detail(@RequestParam Long id) {
        return ApiResult.success(productBundleService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "product.bundle.import")
    @PostMapping("/product-bundle/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        productBundleService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "product.bundle.export")
    @PostMapping("/product-bundle/export")
    public void exportExcel(@RequestBody ProductBundleListDTO dto, HttpServletResponse response) {
        productBundleService.exportExcel(dto, response);
    }
}