package com.glowxq.rescue.work.pojo.dto;

import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.work.pojo.po.Work;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
/**
 * <p>
 * Work导入DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Schema(description = "Work导入DTO")
public class WorkImportDTO implements BaseDTO {

    @ExcelProperty(value = "租户id")
    @Schema(description = "租户id")
    private String tenantId;

    @ExcelProperty(value = "部门ID")
    @Schema(description = "部门ID")
    private Long deptId;

    @ExcelProperty(value = "分类id")
    @Schema(description = "分类id")
    private Long categoryId;

    @ExcelProperty(value = "任务编号")
    @Schema(description = "任务编号")
    private String workNumber;

    @ExcelProperty(value = "标题")
    @Schema(description = "标题")
    private String title;

    @ExcelProperty(value = "详情")
    @Schema(description = "详情")
    private String content;

    @ExcelProperty(value = "报名要求")
    @Schema(description = "报名要求")
    private String applyRequire;

    @ExcelProperty(value = "负责人ID")
    @Schema(description = "负责人ID")
    private Long principalUserId;

    @ExcelProperty(value = "负责人")
    @Schema(description = "负责人")
    private String principalName;

    @ExcelProperty(value = "负责人联系方式")
    @Schema(description = "负责人联系方式")
    private String principalPhone;

    @ExcelProperty(value = "求助人")
    @Schema(description = "求助人")
    private String askName;

    @ExcelProperty(value = "求助人联系方式")
    @Schema(description = "求助人联系方式")
    private String askContact;

    @ExcelProperty(value = "省市区地址")
    @Schema(description = "省市区地址")
    private String address;

    @ExcelProperty(value = "详细地址")
    @Schema(description = "详细地址")
    private String detailAddress;

    @ExcelProperty(value = "经度")
    @Schema(description = "经度")
    private Integer longitude;

    @ExcelProperty(value = "纬度")
    @Schema(description = "纬度")
    private Integer latitude;

    @ExcelProperty(value = "备注")
    @Schema(description = "备注")
    private String remark;

    @ExcelProperty(value = "启用")
    @Schema(description = "启用")
    private Boolean enable;

    @ExcelProperty(value = "状态")
    @Schema(description = "状态")
    private String status;

    @ExcelProperty(value = "签到检查地址")
    @Schema(description = "签到检查地址")
    private Boolean enableCheckAddress;

    @ExcelProperty(value = "签到密码校验")
    @Schema(description = "签到密码校验")
    private Boolean enableCheckPassword;

    @ExcelProperty(value = "时间校验")
    @Schema(description = "时间校验")
    private Boolean enableCheckTime;

    @ExcelProperty(value = "签到密码")
    @Schema(description = "签到密码")
    private String signOnPassword;

    @ExcelProperty(value = "签退密码")
    @Schema(description = "签退密码")
    private String signExitPassword;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Override
    public Work buildEntity() {
        return BeanCopyUtils.copy(this, Work.class);
    }
}