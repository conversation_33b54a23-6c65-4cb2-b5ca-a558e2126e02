package com.glowxq.rescue.work.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.rescue.work.pojo.dto.WorkSignCreateDTO;
import com.glowxq.rescue.work.pojo.dto.WorkSignDTO;
import com.glowxq.rescue.work.pojo.dto.WorkSignListDTO;
import com.glowxq.rescue.work.pojo.dto.WorkSignUpdateDTO;
import com.glowxq.rescue.work.pojo.vo.WorkSignVO;
import com.glowxq.rescue.work.service.WorkSignService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * work/任务签到 Api
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Tag(name = "任务签到")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class WorkSignController extends BaseApi {

    private final WorkSignService workSignService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "work.sign.create")
    @PostMapping("/work-sign/create")
    public ApiResult<Void> create(@RequestBody WorkSignCreateDTO dto) {
        workSignService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "新增")
    @SaCheckPermission(value = "work.sign.create")
    @PostMapping("/work-sign/sign")
    public ApiResult<Void> sign(@RequestBody WorkSignDTO dto) {
        workSignService.sign(dto);
        return ApiResult.success();
    }


    @Operation(summary = "修改")
    @SaCheckPermission(value = "work.sign.update")
    @PutMapping("/work-sign/update")
    public ApiResult<Void> update(@RequestBody WorkSignUpdateDTO dto) {
        workSignService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "work.sign.remove")
    @DeleteMapping("/work-sign/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        workSignService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "work.sign.query_table")
    @GetMapping("/work-sign/list")
    public ApiResult<PageResult<WorkSignVO>> list(WorkSignListDTO dto) {
        return ApiPageResult.success(workSignService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "work.sign.query_table")
    @GetMapping("/work-sign/detail")
    public ApiResult<WorkSignVO> detail(@RequestParam Long id) {
        return ApiResult.success(workSignService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "work.sign.import")
    @PostMapping("/work-sign/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        workSignService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "work.sign.export")
    @PostMapping("/work-sign/export")
    public void exportExcel(@RequestBody WorkSignListDTO dto, HttpServletResponse response) {
        workSignService.exportExcel(dto, response);
    }
}