后端完成了物资申请页面所需的接口,请根据markdown文件的内容，请帮我创建物资申请页面，并完善逻辑和相关功能，注意样式和交互要美观以Apple为准

* 物资选择所需数据的接口：/product/select
* 创建物资申请单接口：/order/create
* 注意物资可以根据标签、仓库、名称、物资编号进行筛选，其中标签是多选使用TagSelect or TagTable组件，仓库是单选使用warehouseSelect组件，物资编号是名称是可以手动输入查询

```java
@Operation(summary = "选择器查询")
@SaCheckPermission(value = "product.query_table")
@PostMapping("/product/select")
public ApiResult<PageResult<ProductSelectVO>> pageSelect(@RequestBody ProductListDTO dto) {
    return ApiPageResult.success(productService.pageSelect(dto));
}
@Data
@Schema(description = "Product查询DTO")
public class ProductListDTO extends PageQuery implements BaseDTO {

    @Schema(description = "仓库id")
    private Long warehouseId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "物资编号")
    private String productNumber;

    @Schema(description = "标签ID")
    private List<Long> tagIds;

    @Override
    public Product buildEntity() {
        return BeanCopyUtils.copy(this, Product.class);
    }
}
public class ProductSelectVO implements BaseVO {

    @ExcelIgnore
    @Schema(description = "物资ID")
    private Long id;

    @ExcelProperty(value = "名称")
    @Schema(description = "名称")
    private String name;

    @ExcelProperty(value = "图片")
    @Schema(description = "图片")
    private String image;

    @ExcelProperty(value = "物资编号")
    @Schema(description = "物资编号")
    private String productNumber;

    @ExcelProperty(value = "启用")
    @Schema(description = "启用")
    private Boolean enable;

    @RelationOneToMany(selfField = "id", targetField = "productId", targetTable = "product_sku")
    private List<ProductSku> productSkus;
}
@Data
@Table(value = "product_sku", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "物资规格")
public class ProductSku implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description = "规格ID")
    private Long id;

    @Schema(description = "物资id")
    private Long productId;

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "规格名称")
    private String name;

    @Schema(description = "规格图片")
    private String image;

    @Schema(description = "库存")
    private Integer stock;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "启用")
    private Boolean enable;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Column(ignore = true)
    private List<ProductLotInsideDTO> productLots;
}
```

* 物资申请订单接口
```java
@Operation(summary = "新增")
@SaCheckPermission(value = "order.create")
@PostMapping("/order/create")
public ApiResult<Void> create(@RequestBody OrderCreateDTO dto) {
    orderService.create(dto);
    return ApiResult.success();
}

@Data
@Schema(description = "Order添加DTO")
public class OrderCreateDTO implements BaseDTO {

    @Schema(description = "任务编号")
    private String workNumber;

    @Schema(description = "类型")
    @InEnum(enumClass = OrderType.class)
    private String type;

    @Schema(description = "操作原因")
    private String reason;

    @Schema(description = "备注")
    private String remark;

    @Size(min = 1, max = 100, message = "最多只能选择100个规格")
    @Schema(description = "skuId")
    private List<SkuQuantityBO> skuQuantityList;
}

@Data
@Schema(description = "Order添加DTO")
@NoArgsConstructor
@AllArgsConstructor
public class SkuQuantityBO implements BaseDTO {

    @Schema(description = "skuId")
    private Long skuId;

    @Schema(description = "数量")
    private Integer quantity;

    @Override
    public Order buildEntity() {
        return BeanCopyUtils.copy(this, Order.class);
    }
}
```