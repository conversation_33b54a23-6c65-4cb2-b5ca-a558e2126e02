package com.glowxq.rescue.product.service;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.rescue.product.pojo.dto.ProductSkuCreateDTO;
import com.glowxq.rescue.product.pojo.dto.ProductSkuListDTO;
import com.glowxq.rescue.product.pojo.dto.ProductSkuUpdateDTO;
import com.glowxq.rescue.product.pojo.po.ProductSku;
import com.glowxq.rescue.product.pojo.vo.ProductSkuVO;
import com.mybatisflex.core.service.IService;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 物资规格 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface ProductSkuService extends IService<ProductSku> {

    void create(ProductSkuCreateDTO dto);

    void update(ProductSkuUpdateDTO dto);

    PageResult<ProductSkuVO> page(ProductSkuListDTO dto);

    List<ProductSkuVO> list(ProductSkuListDTO dto);

    void remove(SelectIdsDTO dto);

    ProductSkuVO detail(Long id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(ProductSkuListDTO dto, HttpServletResponse response);

    void deleteByProductId(Long id);

}