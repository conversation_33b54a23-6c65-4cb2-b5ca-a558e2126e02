package com.glowxq.rescue.work.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.rescue.work.enums.SignType;
import com.glowxq.rescue.work.pojo.po.WorkSign;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * WorkSign添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Schema(description = "WorkSign添加DTO")
public class WorkSignDTO implements BaseDTO {

    @Schema(description = "任务ID")
    private Long workId;

    @Schema(description = "签到/签退")
    private String signType;

    @Schema(description = "签到密码")
    private String signOnPassword;

    @Schema(description = "签退密码")
    private String signExitPassword;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String latitude;

    @Override
    public WorkSign buildEntity() {
        return BeanCopyUtils.copy(this, WorkSign.class);
    }

    public SignType signType() {
        return SignType.matchCode(signType);
    }
}