package com.glowxq.rescue.order.service;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.rescue.order.pojo.dto.OrderItemCreateDTO;
import com.glowxq.rescue.order.pojo.dto.OrderItemListDTO;
import com.glowxq.rescue.order.pojo.dto.OrderItemUpdateDTO;
import com.glowxq.rescue.order.pojo.po.OrderItem;
import com.glowxq.rescue.order.pojo.vo.OrderItemVO;
import com.mybatisflex.core.service.IService;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 订单子项 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface OrderItemService extends IService<OrderItem> {

    void create(OrderItemCreateDTO dto);

    void update(OrderItemUpdateDTO dto);

    PageResult<OrderItemVO> page(OrderItemListDTO dto);

    List<OrderItemVO> list(OrderItemListDTO dto);

    void remove(SelectIdsDTO dto);

    OrderItemVO detail(Long id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(OrderItemListDTO dto, HttpServletResponse response);

    List<OrderItem> listByOrderId(Long orderId);
}