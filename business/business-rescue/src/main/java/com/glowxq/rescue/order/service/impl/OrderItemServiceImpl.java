package com.glowxq.rescue.order.service.impl;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.PageUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.rescue.order.mapper.OrderItemMapper;
import com.glowxq.rescue.order.pojo.dto.OrderItemCreateDTO;
import com.glowxq.rescue.order.pojo.dto.OrderItemImportDTO;
import com.glowxq.rescue.order.pojo.dto.OrderItemListDTO;
import com.glowxq.rescue.order.pojo.dto.OrderItemUpdateDTO;
import com.glowxq.rescue.order.pojo.po.OrderItem;
import com.glowxq.rescue.order.pojo.vo.OrderItemVO;
import com.glowxq.rescue.order.service.OrderItemService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 订单子项 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Service
@RequiredArgsConstructor
public class OrderItemServiceImpl extends ServiceImpl<OrderItemMapper, OrderItem> implements OrderItemService {

    @Override
    public void create(OrderItemCreateDTO dto) {
        OrderItem orderItem = BeanCopyUtils.copy(dto, OrderItem.class);
        save(orderItem);
    }

    @Override
    public void update(OrderItemUpdateDTO dto) {
        OrderItem orderItem = BeanCopyUtils.copy(dto, OrderItem.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
                              .eq(OrderItem::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(orderItem);
    }

    @Override
    public PageResult<OrderItemVO> page(OrderItemListDTO dto) {
        Page<OrderItemVO> page = pageAs(PageUtils.getPage(dto), buildQueryWrapper(dto), OrderItemVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<OrderItemVO> list(OrderItemListDTO dto) {
        return listAs(buildQueryWrapper(dto), OrderItemVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public OrderItemVO detail(Long id) {
        OrderItem orderItem = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(orderItem);
        return BeanCopyUtils.copy(orderItem, OrderItemVO.class);
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<OrderItemImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), OrderItemImportDTO.class, true);
        List<OrderItemImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(OrderItemListDTO dto, HttpServletResponse response) {
        List<OrderItemVO> list = list(dto);
        String fileName = "订单子项模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "订单子项", OrderItemVO.class, os);
    }

    @Override
    public List<OrderItem> listByOrderId(Long orderId) {
        return mapper.listByOrderId(orderId);
    }

    private static QueryWrapper buildQueryWrapper(OrderItemListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(OrderItem.class);
        wrapper.eq(OrderItem::getOrderId, dto.getOrderId(), Utils.isNotNull(dto.getOrderId()));
        wrapper.eq(OrderItem::getTenantId, dto.getTenantId(), Utils.isNotNull(dto.getTenantId()));
        wrapper.eq(OrderItem::getDeptId, dto.getDeptId(), Utils.isNotNull(dto.getDeptId()));
        wrapper.eq(OrderItem::getProductId, dto.getProductId(), Utils.isNotNull(dto.getProductId()));
        wrapper.like(OrderItem::getProductName, dto.getProductName(), Utils.isNotNull(dto.getProductName()));
        wrapper.eq(OrderItem::getImage, dto.getImage(), Utils.isNotNull(dto.getImage()));
        wrapper.eq(OrderItem::getSkuId, dto.getSkuId(), Utils.isNotNull(dto.getSkuId()));
        wrapper.like(OrderItem::getSkuName, dto.getSkuName(), Utils.isNotNull(dto.getSkuName()));
        wrapper.eq(OrderItem::getSkuImage, dto.getSkuImage(), Utils.isNotNull(dto.getSkuImage()));
        wrapper.eq(OrderItem::getQuantity, dto.getQuantity(), Utils.isNotNull(dto.getQuantity()));
        wrapper.eq(OrderItem::getPrice, dto.getPrice(), Utils.isNotNull(dto.getPrice()));
        return wrapper;
    }
}