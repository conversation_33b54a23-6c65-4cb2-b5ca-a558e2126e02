package com.glowxq.latex.service;

import com.glowxq.latex.config.LatexConverterConfig;
import com.glowxq.latex.service.impl.SimpleTexLatexConverter;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;

/**
 * SimpleTex OCR接口测试
 * 测试新增的通用OCR功能
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@SpringBootTest
@TestPropertySource(properties = {
        "latex.converter.api-type=general_ocr",
        "latex.converter.uat-token=test_token",
        "latex.converter.general-ocr.rec-mode=auto",
        "latex.converter.general-ocr.enable-img-rotation=true"
})
public class SimpleTexOcrTest {

    @Test
    void testLatexOcrConfig() {
        System.out.println("=== 测试LaTeX OCR配置 ===");
        
        LatexConverterConfig config = new LatexConverterConfig();
        config.setApiType("latex_ocr");
        config.setUatToken("test_token");
        
        SimpleTexLatexConverter converter = new SimpleTexLatexConverter(config);
        
        System.out.println("接口类型: " + config.getApiType());
        System.out.println("服务名称: " + converter.getServiceName());
        System.out.println("配置信息: " + converter.getConfigInfo());
        System.out.println("服务可用性: " + converter.isServiceAvailable());
    }

    @Test
    void testGeneralOcrConfig() {
        System.out.println("=== 测试通用OCR配置 ===");
        
        LatexConverterConfig config = new LatexConverterConfig();
        config.setApiType("general_ocr");
        config.setUatToken("test_token");
        
        // 配置通用OCR参数
        LatexConverterConfig.GeneralOcrConfig ocrConfig = config.getGeneralOcr();
        ocrConfig.setRecMode("formula");
        ocrConfig.setEnableImgRotation(true);
        ocrConfig.setInlineFormulaWrapper(Arrays.asList("$", "$"));
        ocrConfig.setIsolatedFormulaWrapper(Arrays.asList("$$", "$$"));
        
        SimpleTexLatexConverter converter = new SimpleTexLatexConverter(config);
        
        System.out.println("接口类型: " + config.getApiType());
        System.out.println("识别模式: " + ocrConfig.getRecMode());
        System.out.println("图片旋转: " + ocrConfig.isEnableImgRotation());
        System.out.println("行内包裹符: " + ocrConfig.getInlineFormulaWrapper());
        System.out.println("独立包裹符: " + ocrConfig.getIsolatedFormulaWrapper());
        System.out.println("配置信息: " + converter.getConfigInfo());
        System.out.println("服务可用性: " + converter.isServiceAvailable());
    }

    @Test
    void testInvalidApiType() {
        System.out.println("=== 测试无效API类型 ===");
        
        LatexConverterConfig config = new LatexConverterConfig();
        config.setApiType("invalid_type");
        config.setUatToken("test_token");
        
        SimpleTexLatexConverter converter = new SimpleTexLatexConverter(config);
        
        System.out.println("接口类型: " + config.getApiType());
        System.out.println("服务可用性: " + converter.isServiceAvailable()); // 应该返回false
    }

    @Test
    void testInvalidRecMode() {
        System.out.println("=== 测试无效识别模式 ===");
        
        LatexConverterConfig config = new LatexConverterConfig();
        config.setApiType("general_ocr");
        config.setUatToken("test_token");
        
        // 设置无效的识别模式
        LatexConverterConfig.GeneralOcrConfig ocrConfig = config.getGeneralOcr();
        ocrConfig.setRecMode("invalid_mode");
        
        SimpleTexLatexConverter converter = new SimpleTexLatexConverter(config);
        
        System.out.println("接口类型: " + config.getApiType());
        System.out.println("识别模式: " + ocrConfig.getRecMode());
        System.out.println("服务可用性: " + converter.isServiceAvailable()); // 应该返回false
    }

    @Test
    void testConfigurationMigration() {
        System.out.println("=== 测试配置迁移兼容性 ===");
        
        // 测试旧配置（不设置api-type）
        LatexConverterConfig oldConfig = new LatexConverterConfig();
        oldConfig.setUatToken("test_token");
        // api-type默认为latex_ocr
        
        SimpleTexLatexConverter converter = new SimpleTexLatexConverter(oldConfig);
        
        System.out.println("默认接口类型: " + oldConfig.getApiType());
        System.out.println("配置信息: " + converter.getConfigInfo());
        System.out.println("向后兼容性: " + ("latex_ocr".equals(oldConfig.getApiType()) ? "✅ 通过" : "❌ 失败"));
    }

    @Test
    void testAllSupportedModes() {
        System.out.println("=== 测试所有支持的识别模式 ===");
        
        String[] modes = {"auto", "document", "formula"};
        
        for (String mode : modes) {
            LatexConverterConfig config = new LatexConverterConfig();
            config.setApiType("general_ocr");
            config.setUatToken("test_token");
            config.getGeneralOcr().setRecMode(mode);
            
            SimpleTexLatexConverter converter = new SimpleTexLatexConverter(config);
            
            System.out.println("模式: " + mode + " - 可用性: " + 
                    (converter.isServiceAvailable() ? "✅" : "❌"));
        }
    }
}
