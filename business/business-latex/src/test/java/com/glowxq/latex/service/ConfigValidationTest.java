package com.glowxq.latex.service;

import com.glowxq.latex.config.LatexConverterConfig;
import com.glowxq.latex.service.impl.SimpleTexLatexConverter;

import java.util.Arrays;

/**
 * 配置验证测试
 * 验证新的SimpleTex OCR配置功能
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
public class ConfigValidationTest {

    public static void main(String[] args) {
        System.out.println("=== SimpleTex OCR 配置验证测试 ===\n");
        
        testLatexOcrConfig();
        testGeneralOcrConfig();
        testInvalidConfigs();
        testConfigMigration();
        
        System.out.println("=== 测试完成 ===");
    }

    private static void testLatexOcrConfig() {
        System.out.println("1. 测试 LaTeX OCR 配置");
        System.out.println("------------------------");
        
        LatexConverterConfig config = new LatexConverterConfig();
        config.setApiType("latex_ocr");
        config.setUatToken("test_token_12345");
        
        SimpleTexLatexConverter converter = new SimpleTexLatexConverter(config);
        
        System.out.println("接口类型: " + config.getApiType());
        System.out.println("配置信息: " + converter.getConfigInfo());
        System.out.println("服务可用性: " + (converter.isServiceAvailable() ? "✅ 可用" : "❌ 不可用"));
        System.out.println();
    }

    private static void testGeneralOcrConfig() {
        System.out.println("2. 测试通用 OCR 配置");
        System.out.println("------------------------");
        
        LatexConverterConfig config = new LatexConverterConfig();
        config.setApiType("general_ocr");
        config.setUatToken("test_token_12345");
        
        // 配置通用OCR参数
        LatexConverterConfig.GeneralOcrConfig ocrConfig = config.getGeneralOcr();
        ocrConfig.setRecMode("formula");
        ocrConfig.setEnableImgRotation(true);
        ocrConfig.setInlineFormulaWrapper(Arrays.asList("$", "$"));
        ocrConfig.setIsolatedFormulaWrapper(Arrays.asList("$$", "$$"));
        
        SimpleTexLatexConverter converter = new SimpleTexLatexConverter(config);
        
        System.out.println("接口类型: " + config.getApiType());
        System.out.println("识别模式: " + ocrConfig.getRecMode());
        System.out.println("图片旋转: " + ocrConfig.isEnableImgRotation());
        System.out.println("行内包裹符: " + ocrConfig.getInlineFormulaWrapper());
        System.out.println("独立包裹符: " + ocrConfig.getIsolatedFormulaWrapper());
        System.out.println("配置信息: " + converter.getConfigInfo());
        System.out.println("服务可用性: " + (converter.isServiceAvailable() ? "✅ 可用" : "❌ 不可用"));
        System.out.println();
    }

    private static void testInvalidConfigs() {
        System.out.println("3. 测试无效配置");
        System.out.println("------------------------");
        
        // 测试无效API类型
        LatexConverterConfig config1 = new LatexConverterConfig();
        config1.setApiType("invalid_type");
        config1.setUatToken("test_token_12345");
        
        SimpleTexLatexConverter converter1 = new SimpleTexLatexConverter(config1);
        System.out.println("无效API类型 '" + config1.getApiType() + "': " + 
                (converter1.isServiceAvailable() ? "❌ 意外可用" : "✅ 正确拒绝"));
        
        // 测试无效识别模式
        LatexConverterConfig config2 = new LatexConverterConfig();
        config2.setApiType("general_ocr");
        config2.setUatToken("test_token_12345");
        config2.getGeneralOcr().setRecMode("invalid_mode");
        
        SimpleTexLatexConverter converter2 = new SimpleTexLatexConverter(config2);
        System.out.println("无效识别模式 '" + config2.getGeneralOcr().getRecMode() + "': " + 
                (converter2.isServiceAvailable() ? "❌ 意外可用" : "✅ 正确拒绝"));
        
        // 测试默认Token
        LatexConverterConfig config3 = new LatexConverterConfig();
        config3.setApiType("latex_ocr");
        // 使用默认的 "your_uat_token"
        
        SimpleTexLatexConverter converter3 = new SimpleTexLatexConverter(config3);
        System.out.println("默认Token配置: " + 
                (converter3.isServiceAvailable() ? "❌ 意外可用" : "✅ 正确拒绝"));
        System.out.println();
    }

    private static void testConfigMigration() {
        System.out.println("4. 测试配置迁移兼容性");
        System.out.println("------------------------");
        
        // 测试默认配置（向后兼容）
        LatexConverterConfig config = new LatexConverterConfig();
        config.setUatToken("test_token_12345");
        // api-type 使用默认值
        
        SimpleTexLatexConverter converter = new SimpleTexLatexConverter(config);
        
        System.out.println("默认接口类型: " + config.getApiType());
        System.out.println("向后兼容性: " + ("latex_ocr".equals(config.getApiType()) ? "✅ 通过" : "❌ 失败"));
        System.out.println("配置信息: " + converter.getConfigInfo());
        
        // 测试所有支持的识别模式
        System.out.println("\n支持的识别模式验证:");
        String[] modes = {"auto", "document", "formula"};
        
        for (String mode : modes) {
            LatexConverterConfig testConfig = new LatexConverterConfig();
            testConfig.setApiType("general_ocr");
            testConfig.setUatToken("test_token_12345");
            testConfig.getGeneralOcr().setRecMode(mode);
            
            SimpleTexLatexConverter testConverter = new SimpleTexLatexConverter(testConfig);
            System.out.println("  " + mode + ": " + 
                    (testConverter.isServiceAvailable() ? "✅ 支持" : "❌ 不支持"));
        }
        System.out.println();
    }
}
