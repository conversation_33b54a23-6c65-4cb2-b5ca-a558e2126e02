package com.glowxq.latex.service;

import com.glowxq.latex.dto.ImagePosition;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 批量列处理功能测试
 * 
 * <AUTHOR>
 * @date 2025/01/24
 */
class BatchColumnsTest {

    /**
     * 测试列排序逻辑
     */
    @Test
    @DisplayName("测试列排序逻辑 - 从右到左")
    void testColumnSorting() {
        // 模拟列信息
        List<ColumnInfo> columns = new ArrayList<>();
        columns.add(new ColumnInfo("A", 0, "A"));
        columns.add(new ColumnInfo("E", 4, "E"));
        columns.add(new ColumnInfo("C", 2, "C"));
        columns.add(new ColumnInfo("B", 1, "B"));

        // 按列索引从大到小排序
        columns.sort((a, b) -> Integer.compare(b.getColumnIndex(), a.getColumnIndex()));

        // 验证排序结果
        assertEquals("E", columns.get(0).getOriginalInput());
        assertEquals("C", columns.get(1).getOriginalInput());
        assertEquals("B", columns.get(2).getOriginalInput());
        assertEquals("A", columns.get(3).getOriginalInput());
    }

    /**
     * 测试列名解析
     */
    @Test
    @DisplayName("测试列名解析功能")
    void testColumnParsing() {
        // 测试基本列名
        assertEquals(0, ImagePosition.getColumnIndex("A"));
        assertEquals(4, ImagePosition.getColumnIndex("E"));
        assertEquals(25, ImagePosition.getColumnIndex("Z"));
        assertEquals(26, ImagePosition.getColumnIndex("AA"));

        // 测试数字索引
        assertEquals(0, ImagePosition.getColumnIndex("0"));
        assertEquals(4, ImagePosition.getColumnIndex("4"));

        // 测试大小写不敏感
        assertEquals(0, ImagePosition.getColumnIndex("a"));
        assertEquals(4, ImagePosition.getColumnIndex("e"));
    }

    /**
     * 测试列名转换
     */
    @Test
    @DisplayName("测试列索引转列名")
    void testIndexToColumnName() {
        assertEquals("A", ImagePosition.getColumnName(0));
        assertEquals("E", ImagePosition.getColumnName(4));
        assertEquals("Z", ImagePosition.getColumnName(25));
        assertEquals("AA", ImagePosition.getColumnName(26));
        assertEquals("AB", ImagePosition.getColumnName(27));
    }

    /**
     * 测试异常情况
     */
    @Test
    @DisplayName("测试异常输入处理")
    void testInvalidInputs() {
        // 测试空输入
        assertThrows(IllegalArgumentException.class, () -> {
            ImagePosition.getColumnIndex("");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            ImagePosition.getColumnIndex(null);
        });

        // 测试无效格式
        assertThrows(IllegalArgumentException.class, () -> {
            ImagePosition.getColumnIndex("A1");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            ImagePosition.getColumnIndex("123A");
        });

        // 测试负数索引
        assertThrows(IllegalArgumentException.class, () -> {
            ImagePosition.getColumnIndex("-1");
        });
    }

    /**
     * 内部类用于测试
     */
    private static class ColumnInfo {
        private final String originalInput;
        private final int columnIndex;
        private final String columnName;

        public ColumnInfo(String originalInput, int columnIndex, String columnName) {
            this.originalInput = originalInput;
            this.columnIndex = columnIndex;
            this.columnName = columnName;
        }

        public String getOriginalInput() {
            return originalInput;
        }

        public int getColumnIndex() {
            return columnIndex;
        }

        public String getColumnName() {
            return columnName;
        }
    }
}
