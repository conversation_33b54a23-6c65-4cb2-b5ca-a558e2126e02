package com.glowxq.latex.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.glowxq.latex.config.LatexConverterConfig;
import com.glowxq.latex.service.impl.SimpleTexLatexConverter;

import java.lang.reflect.Method;

/**
 * 响应解析测试
 * 测试不同接口的响应解析功能
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
public class ResponseParsingTest {

    public static void main(String[] args) {
        System.out.println("=== SimpleTex 响应解析测试 ===\n");
        
        testLatexOcrResponse();
        testGeneralOcrResponse();
        testErrorResponse();
        
        System.out.println("=== 测试完成 ===");
    }

    private static void testLatexOcrResponse() {
        System.out.println("1. 测试 LaTeX OCR 响应解析");
        System.out.println("---------------------------");
        
        String latexOcrResponse = """
        {
          "status": true,
          "res": {
            "latex": "\\\\frac{x^2 + y^2}{z}"
          },
          "request_id": "tr_12345"
        }
        """;
        
        LatexConverterConfig config = new LatexConverterConfig();
        config.setApiType("latex_ocr");
        config.setUatToken("test_token");
        
        try {
            String result = parseResponseUsingReflection(config, latexOcrResponse);
            System.out.println("✅ LaTeX OCR 解析成功");
            System.out.println("结果: " + result);
        } catch (Exception e) {
            System.out.println("❌ LaTeX OCR 解析失败: " + e.getMessage());
        }
        System.out.println();
    }

    private static void testGeneralOcrResponse() {
        System.out.println("2. 测试通用 OCR 响应解析");
        System.out.println("---------------------------");
        
        // 实际的通用OCR响应格式
        String generalOcrResponse = """
        {
          "status": true,
          "res": {
            "type": "doc",
            "info": {
              "markdown": "$\\\\boxed{9.1}$ 设$n$、$k($其中$n>k)$ 是$a$ 的两个首要正因数。则$\\\\frac an$与$\\\\frac ak$ 分别是$a$ 的次小的与\\n\\n最小的大于1 的正因数。设 p 是$a$ 的最小质因数，而 $q$ 是$a$ 的除了 p 以外的最小质因数(如果它存在),那么$\\\\frac an=p$。而$\\\\frac ak$是质数(此时它等于$q$),或者是合数。在第二种情形下，$\\\\frac ak$的唯一质因数是$p$,因此$\\\\frac ak=p^2$。这种情形只能是$a$ 可被$p^2\\\\textbf{整 除 , 并 且 }p^2<$ $q$或者$q$根本不存在。\\n如此一来，$a$ 的首要正因数或者是$\\\\frac ap$和$\\\\frac aq$,或者是$\\\\frac ap$和$\\\\frac a{p^2}$。下面我们说明，可以根据两个首要正因数$n$、$k$唯一地恢复$a$本身(由此即可得出题中结论)。事实上，如果$n$ 可被$k$ 整除，则就是上面所说的第二种情形，即$\\\\frac a{p^2}=k$,于是$a=kp^2=\\\\frac{n^2}k$。如果是第一种情形，则$a=\\\\sharp[n,k],即n$与$k$的最小公倍数。"
            }
          },
          "request_id": "tr_17533390055511243530982809834"
        }
        """;
        
        LatexConverterConfig config = new LatexConverterConfig();
        config.setApiType("general_ocr");
        config.setUatToken("test_token");
        
        try {
            String result = parseResponseUsingReflection(config, generalOcrResponse);
            System.out.println("✅ 通用 OCR 解析成功");
            System.out.println("结果长度: " + result.length());
            System.out.println("结果预览: " + result.substring(0, Math.min(100, result.length())) + "...");
        } catch (Exception e) {
            System.out.println("❌ 通用 OCR 解析失败: " + e.getMessage());
        }
        System.out.println();
    }

    private static void testErrorResponse() {
        System.out.println("3. 测试错误响应解析");
        System.out.println("---------------------");
        
        String errorResponse = """
        {
          "status": false,
          "message": "Invalid token"
        }
        """;
        
        LatexConverterConfig config = new LatexConverterConfig();
        config.setApiType("latex_ocr");
        config.setUatToken("test_token");
        
        try {
            String result = parseResponseUsingReflection(config, errorResponse);
            System.out.println("❌ 错误响应应该抛出异常，但返回了: " + result);
        } catch (Exception e) {
            System.out.println("✅ 错误响应正确抛出异常: " + e.getMessage());
        }
        System.out.println();
    }

    /**
     * 使用反射调用私有的parseResponse方法
     */
    private static String parseResponseUsingReflection(LatexConverterConfig config, String responseBody) throws Exception {
        SimpleTexLatexConverter converter = new SimpleTexLatexConverter(config);
        
        // 使用反射访问私有方法
        Method parseResponseMethod = SimpleTexLatexConverter.class.getDeclaredMethod("parseResponse", String.class);
        parseResponseMethod.setAccessible(true);
        
        return (String) parseResponseMethod.invoke(converter, responseBody);
    }
}
