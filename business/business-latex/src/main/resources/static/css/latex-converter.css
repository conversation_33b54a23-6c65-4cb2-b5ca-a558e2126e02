/* LaTeX转换工具样式文件 */

/* 基础重置和变量 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --success-color: #059669;
    --error-color: #dc2626;
    --warning-color: #d97706;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --border-radius: 8px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --transition: all 0.2s ease-in-out;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* 容器布局 */
.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* 页面头部 */
.header {
    text-align: center;
    margin-bottom: 2rem;
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    position: relative;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.icon {
    font-size: 2rem;
}

.description {
    font-size: 1.1rem;
    color: var(--gray-600);
    margin-bottom: 1rem;
}

.version {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Tab导航 */
.tab-nav {
    display: flex;
    background: white;
    border-radius: var(--border-radius);
    padding: 0.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-md);
    gap: 0.5rem;
}

.tab-btn {
    flex: 1;
    padding: 1rem;
    border: none;
    background: transparent;
    border-radius: calc(var(--border-radius) - 2px);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    color: var(--gray-600);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.tab-btn:hover {
    background: var(--gray-50);
    color: var(--gray-800);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.tab-icon {
    font-size: 1.2rem;
}

/* Tab内容 */
.tab-content {
    margin-bottom: 2rem;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 表单卡片 */
.form-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-md);
}

.form-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.form-desc {
    color: var(--gray-600);
    margin-bottom: 2rem;
    line-height: 1.5;
}

/* 表单元素 */
.form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-weight: 500;
    color: var(--gray-700);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.label-icon {
    font-size: 1.1rem;
}

.form-input,
.form-textarea {
    padding: 0.75rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: white;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-input.error,
.form-textarea.error {
    border-color: var(--error-color);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* 列输入组 */
.column-inputs {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.column-input-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.column-input {
    flex: 1;
}

.btn-remove {
    padding: 0.5rem;
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-remove:hover:not(:disabled) {
    background: #b91c1c;
}

.btn-remove:disabled {
    background: var(--gray-300);
    cursor: not-allowed;
}

.btn-add {
    align-self: flex-start;
    padding: 0.5rem 1rem;
    background: var(--gray-100);
    color: var(--gray-700);
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.btn-add:hover {
    background: var(--gray-200);
    border-color: var(--gray-400);
}

/* 提交按钮 */
.btn-submit {
    padding: 1rem 2rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.btn-submit:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-submit:disabled {
    background: var(--gray-400);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-icon {
    font-size: 1.2rem;
}

/* 表单提示和错误 */
.form-hint {
    font-size: 0.875rem;
    color: var(--gray-500);
    margin-top: 0.25rem;
}

.hint-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.hint-list {
    margin-left: 1rem;
    list-style-type: disc;
}

.hint-list li {
    margin-bottom: 0.125rem;
}

.input-error {
    color: var(--error-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: none;
}

.input-error.show {
    display: block;
}

/* 结果展示 */
.result-section {
    margin-top: 2rem;
}

.result-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-md);
}

.result-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.result-content {
    line-height: 1.6;
}

.result-success {
    padding: 1rem;
    background: #ecfdf5;
    border: 1px solid #a7f3d0;
    border-radius: var(--border-radius);
    color: var(--success-color);
}

.result-error {
    padding: 1rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: var(--border-radius);
    color: var(--error-color);
}

.result-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-top: 0.25rem;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--gray-700);
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .header {
        padding: 1.5rem;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .form-card {
        padding: 1.5rem;
    }
    
    .tab-btn {
        padding: 0.75rem;
        font-size: 0.875rem;
    }
    
    .column-input-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn-remove {
        align-self: flex-end;
        width: auto;
        padding: 0.5rem 1rem;
    }
}
