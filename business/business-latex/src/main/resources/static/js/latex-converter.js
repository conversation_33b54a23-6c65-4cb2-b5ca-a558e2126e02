/**
 * LaTeX转换工具前端交互脚本
 */

class LatexConverter {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.initColumnInputs();
    }

    bindEvents() {
        // Tab切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // 添加列按钮
        document.getElementById('addColumnBtn').addEventListener('click', () => this.addColumnInput());

        // 表单提交
        document.getElementById('columnForm').addEventListener('submit', (e) => this.handleColumnSubmit(e));
        document.getElementById('cellForm').addEventListener('submit', (e) => this.handleCellSubmit(e));

        // 输入验证
        document.getElementById('columnTableUrl').addEventListener('blur', (e) => this.validateUrl(e.target, 'columnTableUrlError'));
        document.getElementById('cellTableUrl').addEventListener('blur', (e) => this.validateUrl(e.target, 'cellTableUrlError'));
        document.getElementById('cellAddresses').addEventListener('blur', (e) => this.validateCellAddresses(e.target));
    }

    initColumnInputs() {
        // 为初始的列输入框绑定事件
        this.bindColumnInputEvents();
    }

    switchTab(tabId) {
        // 切换tab按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

        // 切换tab内容
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        document.getElementById(tabId).classList.add('active');

        // 隐藏结果区域
        this.hideResult();
    }

    addColumnInput() {
        const container = document.getElementById('columnInputs');
        const inputGroup = document.createElement('div');
        inputGroup.className = 'column-input-group';
        
        inputGroup.innerHTML = `
            <input 
                type="text" 
                name="columns[]" 
                class="form-input column-input" 
                placeholder="列名，如：A 或 姓名"
                required
            >
            <button type="button" class="btn-remove">
                <span>✕</span>
            </button>
        `;
        
        container.appendChild(inputGroup);
        this.bindColumnInputEvents();
        this.updateRemoveButtons();
    }

    bindColumnInputEvents() {
        // 为所有删除按钮绑定事件
        document.querySelectorAll('.btn-remove').forEach(btn => {
            btn.removeEventListener('click', this.removeColumnInput); // 避免重复绑定
            btn.addEventListener('click', (e) => this.removeColumnInput(e));
        });
    }

    removeColumnInput = (e) => {
        const inputGroup = e.target.closest('.column-input-group');
        inputGroup.remove();
        this.updateRemoveButtons();
    }

    updateRemoveButtons() {
        const removeButtons = document.querySelectorAll('.btn-remove');
        removeButtons.forEach((btn, index) => {
            btn.disabled = removeButtons.length === 1;
        });
    }

    validateUrl(input, errorId) {
        const errorElement = document.getElementById(errorId);
        const url = input.value.trim();
        
        if (!url) {
            this.showError(input, errorElement, '请输入表格链接');
            return false;
        }

        // 简单的飞书链接格式验证
        const feishuPattern = /^https:\/\/.*\.feishu\.cn\/wiki\/[a-zA-Z0-9]+/;
        if (!feishuPattern.test(url)) {
            this.showError(input, errorElement, '请输入有效的飞书表格链接');
            return false;
        }

        this.clearError(input, errorElement);
        return true;
    }

    validateCellAddresses(input) {
        const errorElement = document.getElementById('cellAddressesError');
        const addresses = input.value.trim();
        
        if (!addresses) {
            this.showError(input, errorElement, '请输入单元格坐标');
            return false;
        }

        // 验证坐标格式
        const addressList = addresses.split(',').map(addr => addr.trim());
        const cellPattern = /^[A-Z]+\d+$/i;
        
        for (let addr of addressList) {
            if (!cellPattern.test(addr)) {
                this.showError(input, errorElement, `坐标格式错误: ${addr}，正确格式如：A1, B2, AA10`);
                return false;
            }
        }

        this.clearError(input, errorElement);
        return true;
    }

    showError(input, errorElement, message) {
        input.classList.add('error');
        errorElement.textContent = message;
        errorElement.classList.add('show');
    }

    clearError(input, errorElement) {
        input.classList.remove('error');
        errorElement.textContent = '';
        errorElement.classList.remove('show');
    }

    async handleColumnSubmit(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        
        // 验证表格链接
        const tableUrlInput = document.getElementById('columnTableUrl');
        if (!this.validateUrl(tableUrlInput, 'columnTableUrlError')) {
            return;
        }

        // 收集列数据
        const columns = Array.from(formData.getAll('columns[]')).filter(col => col.trim());
        if (columns.length === 0) {
            alert('请至少添加一个列');
            return;
        }

        const requestData = {
            tableUrl: formData.get('tableUrl'),
            columns: columns
        };

        await this.submitRequest(window.APP_CONFIG.parseTableJsonUrl, requestData, 'columnSubmitBtn');
    }

    async handleCellSubmit(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        
        // 验证表格链接
        const tableUrlInput = document.getElementById('cellTableUrl');
        if (!this.validateUrl(tableUrlInput, 'cellTableUrlError')) {
            return;
        }

        // 验证坐标
        const cellAddressesInput = document.getElementById('cellAddresses');
        if (!this.validateCellAddresses(cellAddressesInput)) {
            return;
        }

        // 处理坐标数据
        const cellAddresses = formData.get('cellAddresses')
            .split(',')
            .map(addr => addr.trim())
            .filter(addr => addr);

        const requestData = {
            tableUrl: formData.get('tableUrl'),
            cellAddresses: cellAddresses
        };

        await this.submitRequest(window.APP_CONFIG.parseTableByCellsUrl, requestData, 'cellSubmitBtn');
    }

    async submitRequest(url, data, buttonId) {
        const button = document.getElementById(buttonId);
        const originalText = button.innerHTML;
        
        try {
            // 显示加载状态
            this.showLoading();
            button.disabled = true;
            button.innerHTML = '<span class="spinner"></span> 处理中...';

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.text();
            
            if (response.ok) {
                this.showResult(result, 'success');
            } else {
                this.showResult(`请求失败: ${result}`, 'error');
            }
        } catch (error) {
            console.error('请求错误:', error);
            this.showResult(`网络错误: ${error.message}`, 'error');
        } finally {
            // 恢复按钮状态
            this.hideLoading();
            button.disabled = false;
            button.innerHTML = originalText;
        }
    }

    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    showResult(message, type) {
        const resultSection = document.getElementById('resultSection');
        const resultContent = document.getElementById('resultContent');
        
        const className = type === 'success' ? 'result-success' : 'result-error';
        resultContent.innerHTML = `<div class="${className}">${this.escapeHtml(message)}</div>`;
        
        resultSection.style.display = 'block';
        resultSection.scrollIntoView({ behavior: 'smooth' });
    }

    hideResult() {
        document.getElementById('resultSection').style.display = 'none';
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new LatexConverter();
});
