<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle}</title>
    <meta name="description" content="${pageDescription}">
    <link rel="stylesheet" href="/static/css/latex-converter.css">
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <header class="header">
            <h1 class="title">
                <span class="icon">🔬</span>
                ${pageTitle}
            </h1>
            <p class="description">${pageDescription}</p>
            <div class="version">v${version}</div>
        </header>

        <!-- Tab导航 -->
        <nav class="tab-nav">
            <button class="tab-btn active" data-tab="column-parse">
                <span class="tab-icon">📊</span>
                按列解析
            </button>
            <button class="tab-btn" data-tab="cell-parse">
                <span class="tab-icon">🎯</span>
                按坐标解析
            </button>
        </nav>

        <!-- Tab内容区域 -->
        <main class="tab-content">
            <!-- 按列解析 -->
            <div id="column-parse" class="tab-pane active">
                <div class="form-card">
                    <h3 class="form-title">按列解析图片转LaTeX</h3>
                    <p class="form-desc">解析指定列中的图片，将其转换为LaTeX表达式并插入到右侧新列中</p>
                    
                    <form id="columnForm" class="form">
                        <div class="form-group">
                            <label for="columnTableUrl" class="form-label">
                                <span class="label-icon">🔗</span>
                                飞书表格链接
                            </label>
                            <input 
                                type="url" 
                                id="columnTableUrl" 
                                name="tableUrl" 
                                class="form-input" 
                                placeholder="请输入飞书表格链接，如：https://xxx.feishu.cn/wiki/token"
                                required
                            >
                            <div class="input-error" id="columnTableUrlError"></div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <span class="label-icon">📋</span>
                                要解析的列
                            </label>
                            <div id="columnInputs" class="column-inputs">
                                <div class="column-input-group">
                                    <input 
                                        type="text" 
                                        name="columns[]" 
                                        class="form-input column-input" 
                                        placeholder="列名，如：A 或 姓名"
                                        required
                                    >
                                    <button type="button" class="btn-remove" disabled>
                                        <span>✕</span>
                                    </button>
                                </div>
                            </div>
                            <button type="button" id="addColumnBtn" class="btn-add">
                                <span class="btn-icon">➕</span>
                                添加列
                            </button>
                            <div class="form-hint">
                                支持列字母（A、B、C）或列名（姓名、年龄）格式
                            </div>
                        </div>

                        <button type="submit" class="btn-submit" id="columnSubmitBtn">
                            <span class="btn-icon">🚀</span>
                            开始转换
                        </button>
                    </form>
                </div>
            </div>

            <!-- 按坐标解析 -->
            <div id="cell-parse" class="tab-pane">
                <div class="form-card">
                    <h3 class="form-title">按坐标解析图片转LaTeX</h3>
                    <p class="form-desc">解析指定坐标位置的图片，将LaTeX表达式写入到右侧单元格中</p>
                    
                    <form id="cellForm" class="form">
                        <div class="form-group">
                            <label for="cellTableUrl" class="form-label">
                                <span class="label-icon">🔗</span>
                                飞书表格链接
                            </label>
                            <input 
                                type="url" 
                                id="cellTableUrl" 
                                name="tableUrl" 
                                class="form-input" 
                                placeholder="请输入飞书表格链接，如：https://xxx.feishu.cn/wiki/token"
                                required
                            >
                            <div class="input-error" id="cellTableUrlError"></div>
                        </div>

                        <div class="form-group">
                            <label for="cellAddresses" class="form-label">
                                <span class="label-icon">🎯</span>
                                单元格坐标
                            </label>
                            <textarea 
                                id="cellAddresses" 
                                name="cellAddresses" 
                                class="form-textarea" 
                                placeholder="请输入单元格坐标，用逗号分隔，如：A1,B2,E1"
                                rows="3"
                                required
                            ></textarea>
                            <div class="form-hint">
                                <div class="hint-title">坐标格式说明：</div>
                                <ul class="hint-list">
                                    <li>支持格式：A1, B2, AA10, AB100 等</li>
                                    <li>多个坐标用逗号分隔</li>
                                    <li>示例：A1,A2,E1 或 A1, B2, C3</li>
                                </ul>
                            </div>
                            <div class="input-error" id="cellAddressesError"></div>
                        </div>

                        <button type="submit" class="btn-submit" id="cellSubmitBtn">
                            <span class="btn-icon">🎯</span>
                            开始转换
                        </button>
                    </form>
                </div>
            </div>
        </main>

        <!-- 结果展示区域 -->
        <section id="resultSection" class="result-section" style="display: none;">
            <div class="result-card">
                <h3 class="result-title">处理结果</h3>
                <div id="resultContent" class="result-content">
                    <!-- 结果内容将通过JavaScript动态填充 -->
                </div>
            </div>
        </section>

        <!-- 加载动画 -->
        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p class="loading-text">正在处理中，请稍候...</p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 传递后端配置到前端
        window.APP_CONFIG = {
            apiBaseUrl: '${apiBaseUrl}',
            parseTableJsonUrl: '${parseTableJsonUrl}',
            parseTableByCellsUrl: '${parseTableByCellsUrl}'
        };
    </script>
    <script src="/static/js/latex-converter.js"></script>
</body>
</html>
