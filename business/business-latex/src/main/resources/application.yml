# LaTeX模块配置文件
spring:
  application:
    name: business-latex

  # FreeMarker模板引擎配置
  freemarker:
    template-loader-path: classpath:/templates/
    suffix: .ftl
    charset: UTF-8
    cache: false  # 开发环境关闭缓存，生产环境建议开启
    settings:
      number_format: 0.##########

# 飞书应用配置
feishu:
  app:
    # 应用ID（需要从飞书开放平台获取）
    app-id: ${FEISHU_APP_ID:your_app_id}
    # 应用密钥（需要从飞书开放平台获取）
    app-secret: ${FEISHU_APP_SECRET:your_app_secret}

