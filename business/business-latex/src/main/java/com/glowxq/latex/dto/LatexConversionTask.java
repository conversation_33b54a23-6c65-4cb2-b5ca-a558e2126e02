package com.glowxq.latex.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * LaTeX转换任务DTO
 * 用于记录图片转LaTeX的任务信息
 * 
 * <AUTHOR>
 * @date 2025/01/23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LatexConversionTask {

    /**
     * 图片位置信息
     */
    private ImagePosition imagePosition;

    /**
     * 图片数据（字节数组）
     */
    private byte[] imageData;

    /**
     * 转换后的LaTeX表达式
     */
    private String latexExpression;

    /**
     * 转换是否成功
     */
    private boolean conversionSuccess;

    /**
     * 错误信息（转换失败时）
     */
    private String errorMessage;

    /**
     * 转换开始时间
     */
    private LocalDateTime startTime;

    /**
     * 转换完成时间
     */
    private LocalDateTime endTime;

    /**
     * 转换耗时（毫秒）
     */
    private long durationMs;

    /**
     * 重试次数
     */
    private int retryCount;

    /**
     * 实际的目标单元格地址（用于新的按坐标处理功能）
     * 当设置此字段时，优先使用此地址而不是从imagePosition计算
     */
    private String targetCellAddress;

    /**
     * 创建成功的转换任务
     */
    public static LatexConversionTask success(ImagePosition imagePosition, byte[] imageData, String latexExpression) {
        return LatexConversionTask.builder()
                .imagePosition(imagePosition)
                .imageData(imageData)
                .latexExpression(latexExpression)
                .conversionSuccess(true)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败的转换任务
     */
    public static LatexConversionTask failure(ImagePosition imagePosition, byte[] imageData, String errorMessage, int retryCount) {
        return LatexConversionTask.builder()
                .imagePosition(imagePosition)
                .imageData(imageData)
                .conversionSuccess(false)
                .errorMessage(errorMessage)
                .retryCount(retryCount)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 计算转换耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.durationMs = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }

    /**
     * 获取目标插入的单元格地址
     * 优先使用设置的targetCellAddress，如果没有则从imagePosition计算
     */
    public String getTargetCellAddress() {
        if (targetCellAddress != null && !targetCellAddress.trim().isEmpty()) {
            return targetCellAddress;
        }
        return imagePosition != null ? imagePosition.getTargetLatexCellAddress() : null;
    }

    /**
     * 获取原始图片的单元格地址
     */
    public String getSourceCellAddress() {
        return imagePosition != null ? imagePosition.getCellAddress() : null;
    }

    @Override
    public String toString() {
        return String.format("LatexConversionTask{%s -> %s, success=%s}", 
                getSourceCellAddress(), getTargetCellAddress(), conversionSuccess);
    }
}
