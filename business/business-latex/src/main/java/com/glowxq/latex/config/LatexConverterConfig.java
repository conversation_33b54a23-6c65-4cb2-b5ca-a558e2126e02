package com.glowxq.latex.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * LaTeX转换器配置类
 * SimpleTex API配置
 *
 * <AUTHOR>
 * @date 2025/01/23
 */
@Data
@Component
@ConfigurationProperties(prefix = "latex.converter")
public class LatexConverterConfig {

    /**
     * SimpleTex API地址（已废弃，由apiType动态生成）
     * @deprecated 使用 apiType 配置替代
     */
    @Deprecated
    private String apiUrl = "https://server.simpletex.cn/api/latex_ocr";

    /**
     * 接口类型：latex_ocr（公式识别）或 general_ocr（通用识别）
     */
    private String apiType = "latex_ocr";

    /**
     * 用户授权令牌（UAT方式）
     * 从 https://simpletex.cn/user/center 获取
     */
    private String uatToken = "your_uat_token";

    /**
     * 请求超时时间（秒）
     */
    private int timeoutSeconds = 30;

    /**
     * 最大重试次数
     */
    private int maxRetries = 3;

    /**
     * 通用OCR专用配置
     */
    private GeneralOcrConfig generalOcr = new GeneralOcrConfig();

    /**
     * 通用OCR配置类
     */
    @Data
    public static class GeneralOcrConfig {
        /**
         * 识别模式：auto（自动检测）, document（文档模式）, formula（公式模式）
         */
        private String recMode = "auto";

        /**
         * 是否启用图片旋转矫正（基于0°，90°, 180°, 270°自动矫正）
         */
        private boolean enableImgRotation = false;

        /**
         * 行内公式包裹符号
         */
        private List<String> inlineFormulaWrapper = Arrays.asList("$", "$");

        /**
         * 独立行公式包裹符号
         */
        private List<String> isolatedFormulaWrapper = Arrays.asList("$$", "$$");
    }
}
