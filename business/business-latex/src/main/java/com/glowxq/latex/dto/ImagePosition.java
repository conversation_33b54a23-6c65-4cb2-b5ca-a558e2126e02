package com.glowxq.latex.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图片位置记录DTO
 * 用于记录图片在表格中的位置信息
 * 
 * <AUTHOR>
 * @date 2025/01/23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImagePosition {

    /**
     * 行号（0-based）
     */
    private int row;

    /**
     * 列号（0-based）
     */
    private int column;

    /**
     * 图片文件token（飞书文件标识）
     */
    private String fileToken;

    /**
     * 列名（A, B, C...）
     */
    private String columnName;

    /**
     * 原始单元格数据（用于调试和日志）
     */
    private Object originalData;

    /**
     * 单元格地址（如：A1, B2）
     */
    private String cellAddress;

    /**
     * 获取目标LaTeX插入位置的列号（原列号+1）
     */
    public int getTargetLatexColumn() {
        return column + 1;
    }

    /**
     * 获取目标LaTeX插入位置的列名
     */
    public String getTargetLatexColumnName() {
        return getColumnName(column + 1);
    }

    /**
     * 获取目标LaTeX插入的单元格地址
     */
    public String getTargetLatexCellAddress() {
        return getTargetLatexColumnName() + (row + 1);
    }

    /**
     * 根据列号获取列名（0->A, 1->B, 2->C...）
     */
    public static String getColumnName(int columnIndex) {
        StringBuilder columnName = new StringBuilder();
        while (columnIndex >= 0) {
            columnName.insert(0, (char) ('A' + columnIndex % 26));
            columnIndex = columnIndex / 26 - 1;
        }
        return columnName.toString();
    }

    /**
     * 将列名转换为列索引（A->0, B->1, C->2, AA->26等）
     * 同时支持直接传入数字字符串作为列索引
     *
     * @param columnName 列名（如"A", "B", "AA"）或列索引字符串（如"0", "1", "26"）
     * @return 列索引（0-based）
     * @throws IllegalArgumentException 当列名格式无效时抛出异常
     */
    public static int getColumnIndex(String columnName) {
        if (columnName == null || columnName.trim().isEmpty()) {
            throw new IllegalArgumentException("列名不能为空");
        }

        String upperColumnName = columnName.trim().toUpperCase();

        // 检查是否为纯数字（列索引）
        if (upperColumnName.matches("^\\d+$")) {
            int index = Integer.parseInt(upperColumnName);
            if (index < 0) {
                throw new IllegalArgumentException("列索引不能为负数: " + index);
            }
            return index;
        }

        // 检查是否为有效的列名格式
        if (!upperColumnName.matches("^[A-Z]+$")) {
            throw new IllegalArgumentException("无效的列名格式: " + columnName);
        }

        int result = 0;
        for (int i = 0; i < upperColumnName.length(); i++) {
            result = result * 26 + (upperColumnName.charAt(i) - 'A' + 1);
        }
        return result - 1; // 转换为0-based索引
    }

    /**
     * 创建ImagePosition的便捷方法
     */
    public static ImagePosition create(int row, int column, String fileToken, Object originalData) {
        String columnName = getColumnName(column);
        String cellAddress = columnName + (row + 1);
        
        return ImagePosition.builder()
                .row(row)
                .column(column)
                .fileToken(fileToken)
                .columnName(columnName)
                .originalData(originalData)
                .cellAddress(cellAddress)
                .build();
    }

    @Override
    public String toString() {
        return String.format("ImagePosition{%s, fileToken='%s'}", cellAddress, fileToken);
    }
}
