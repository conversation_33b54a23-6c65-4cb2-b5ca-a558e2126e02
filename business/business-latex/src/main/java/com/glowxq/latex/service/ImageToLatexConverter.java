package com.glowxq.latex.service;

import com.glowxq.core.common.exception.common.AlertsException;
import java.util.List;

/**
 * 图片转LaTeX转换服务接口
 * 基于SimpleTex API的图片转LaTeX功能
 *
 * <AUTHOR>
 * @date 2025/01/23
 */
public interface ImageToLatexConverter {

    /**
     * 将单个图片转换为LaTeX表达式
     *
     * @param imageData 图片数据（字节数组）
     * @param fileName 文件名（用于调试和日志）
     * @return LaTeX表达式字符串
     * @throws AlertsException 转换失败时抛出异常
     */
    String convertImageToLatex(byte[] imageData, String fileName) throws AlertsException;

    /**
     * 批量转换图片为LaTeX表达式
     *
     * @param imageDataList 图片数据列表
     * @param fileNames 对应的文件名列表
     * @return LaTeX表达式列表，与输入顺序对应
     * @throws Exception 转换失败时抛出异常
     */
    List<String> batchConvertImageToLatex(List<byte[]> imageDataList, List<String> fileNames) throws Exception;

    /**
     * 检查转换服务是否可用
     *
     * @return true表示服务可用，false表示不可用
     */
    boolean isServiceAvailable();

    /**
     * 获取服务名称
     *
     * @return 服务名称
     */
    String getServiceName();

    /**
     * 获取服务支持的最大图片大小（字节）
     *
     * @return 最大支持的图片大小
     */
    long getMaxImageSize();

    /**
     * 检查图片格式是否支持
     *
     * @param imageData 图片数据
     * @return true表示支持，false表示不支持
     */
    boolean isSupportedImageFormat(byte[] imageData);

    /**
     * 获取服务的配置信息（用于调试）
     *
     * @return 配置信息字符串
     */
    String getConfigInfo();
}
