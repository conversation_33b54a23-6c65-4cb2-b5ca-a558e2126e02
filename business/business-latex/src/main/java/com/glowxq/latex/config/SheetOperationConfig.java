package com.glowxq.latex.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 表格操作配置类
 * 
 * <AUTHOR>
 * @date 2025/01/23
 */
@Data
@Component
@ConfigurationProperties(prefix = "latex.sheet-operation")
public class SheetOperationConfig {

    /**
     * 是否启用事务性操作
     */
    private boolean transactionEnabled = true;

    /**
     * 操作失败时是否回滚
     */
    private boolean rollbackOnFailure = true;

    /**
     * 批量操作的批次大小
     */
    private int batchSize = 50;
}
