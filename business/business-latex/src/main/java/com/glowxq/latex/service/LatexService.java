package com.glowxq.latex.service;

import com.glowxq.core.common.exception.common.AlertsException;
import com.glowxq.latex.dto.*;
import com.lark.oapi.Client;
import com.lark.oapi.core.response.RawResponse;
import com.lark.oapi.core.token.AccessTokenType;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.drive.v1.model.DownloadMediaReq;
import com.lark.oapi.service.drive.v1.model.DownloadMediaResp;
import com.lark.oapi.service.sheets.v3.model.QuerySpreadsheetSheetReq;
import com.lark.oapi.service.sheets.v3.model.QuerySpreadsheetSheetResp;
import com.lark.oapi.service.sheets.v3.model.Sheet;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpaceReq;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpaceResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * LaTeX服务类 - 提供飞书API集成功能
 *
 * <AUTHOR>
 * @date 2025/01/22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LatexService {

    private final Client feishuClient;

    private final ImageToLatexConverter imageToLatexConverter;

    private final SheetOperationService sheetOperationService;

    /**
     * 从Wiki节点获取spreadsheet_token
     *
     * @param wikiToken Wiki节点token
     * @return spreadsheet_token
     *
     * @throws RuntimeException 当API调用失败时抛出异常
     */
    public String getSpreadsheetTokenFromWiki(String wikiToken) {
        log.info("开始从Wiki节点获取spreadsheet_token, wikiToken: {}", wikiToken);

        try {
            // 创建请求对象
            GetNodeSpaceReq req = GetNodeSpaceReq.newBuilder()
                                                 .token(wikiToken)
                                                 .objType("wiki")
                                                 .build();

            // 发起请求 - 使用tenant_access_token（SDK自动处理）
            GetNodeSpaceResp resp = feishuClient.wiki().v2().space().getNode(req);

            // 处理服务端错误
            if (!resp.success()) {
                String errorMsg = String.format("获取Wiki节点失败: code:%s, msg:%s, reqId:%s",
                        resp.getCode(), resp.getMsg(), resp.getRequestId());
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }

            // 从响应中提取obj_token（即spreadsheet_token）
            String objToken = resp.getData().getNode().getObjToken();
            log.info("成功获取spreadsheet_token: {}", objToken);

            return objToken;
        } catch (Exception e) {
            log.error("从Wiki节点获取spreadsheet_token时发生异常", e);
            throw new RuntimeException("获取spreadsheet_token失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据spreadsheet_token获取所有sheet的ID列表
     *
     * @param spreadsheetToken spreadsheet令牌
     * @return sheet ID列表
     *
     * @throws RuntimeException 当API调用失败时抛出异常
     */
    public List<String> getSheetIds(String spreadsheetToken) {
        log.info("开始获取工作表sheet_id列表, spreadsheetToken: {}", spreadsheetToken);

        try {
            // 创建请求对象
            QuerySpreadsheetSheetReq req = QuerySpreadsheetSheetReq.newBuilder()
                                                                   .spreadsheetToken(spreadsheetToken)
                                                                   .build();

            // 发起请求获取工作表信息 - 使用tenant_access_token（SDK自动处理）
            QuerySpreadsheetSheetResp resp = feishuClient.sheets().v3().spreadsheetSheet()
                                                         .query(req);

            // 处理服务端错误
            if (!resp.success()) {
                String errorMsg = String.format("获取工作表信息失败: code:%s, msg:%s, reqId:%s",
                        resp.getCode(), resp.getMsg(), resp.getRequestId());
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }

            // 提取所有sheet的ID
            List<String> sheetIds = new ArrayList<>();
            Sheet[] sheets = resp.getData().getSheets();

            if (sheets != null) {
                for (Sheet sheet : sheets) {
                    sheetIds.add(sheet.getSheetId());
                    log.debug("找到sheet: id={}, title={}", sheet.getSheetId(), sheet.getTitle());
                }
            }

            log.info("成功获取到{}个工作表的sheet_id", sheetIds.size());
            return sheetIds;
        } catch (Exception e) {
            log.error("获取工作表sheet_id时发生异常", e);
            throw new RuntimeException("获取sheet_id失败: " + e.getMessage(), e);
        }
    }

    /**
     * 直接从Wiki节点获取工作表的sheet_id列表（组合方法）
     *
     * @param wikiToken Wiki节点token
     * @return sheet ID列表
     *
     * @throws RuntimeException 当API调用失败时抛出异常
     */
    public List<String> getSheetIdFromWiki(String wikiToken) {
        log.info("开始从Wiki节点直接获取sheet_id列表, wikiToken: {}", wikiToken);

        // 第一步：从Wiki获取spreadsheet_token
        String spreadsheetToken = getSpreadsheetTokenFromWiki(wikiToken);

        // 第二步：根据spreadsheet_token获取sheet_id列表
        return getSheetIds(spreadsheetToken);
    }

    /**
     * 从飞书Wiki URL中提取token
     *
     * @param wikiUrl 飞书Wiki链接
     * @return 提取出的token
     *
     * @throws RuntimeException 当链接格式不正确时抛出异常
     */
    public String extractTokenFromWikiUrl(String wikiUrl) {
        log.info("开始从Wiki链接提取token, 链接: {}", wikiUrl);

        if (wikiUrl == null || wikiUrl.trim().isEmpty()) {
            throw new RuntimeException("Wiki链接不能为空");
        }

        // 正则表达式匹配飞书Wiki链接中的token
        // 支持多种格式：
        // https://xxx.feishu.cn/wiki/TOKEN
        // https://xxx.feishu.cn/wiki/TOKEN?xxx
        // https://xxx.feishu.cn/wiki/TOKEN#xxx
        Pattern pattern = Pattern.compile("https?://[^/]+\\.feishu\\.cn/wiki/([a-zA-Z0-9]+)");
        Matcher matcher = pattern.matcher(wikiUrl);

        if (matcher.find()) {
            String token = matcher.group(1);
            log.info("成功提取token: {}", token);
            return token;
        }

        throw new AlertsException("无法从链接中提取token，请检查链接格式是否正确");
    }

    /**
     * 直接从Wiki链接获取工作表的sheet_id列表（组合方法）
     *
     * @param wikiUrl 飞书Wiki链接
     * @return sheet ID列表
     *
     * @throws RuntimeException 当API调用失败时抛出异常
     */
    public List<String> getSheetIdFromWikiUrl(String wikiUrl) {
        log.info("开始从Wiki链接直接获取sheet_id列表, 链接: {}", wikiUrl);

        // 第一步：从Wiki链接提取token
        String wikiToken = extractTokenFromWikiUrl(wikiUrl);

        // 第二步：根据token获取sheet_id列表
        return getSheetIdFromWiki(wikiToken);
    }

    /**
     * 测试方法：将A列的图片解析然后在B列插入对应的LaTeX表达式
     *
     * @param wikiUrl 飞书Wiki链接
     */
    public void test(String wikiUrl) {
        test(wikiUrl, "A"); // 默认使用A列，保持向后兼容
    }

    /**
     * 批量处理多列图片转LaTeX（插入列模式）
     * 从右到左处理列，避免列索引偏移问题
     *
     * @param wikiUrl 飞书Wiki链接
     * @param columns 要处理的列数组
     * @return 处理结果
     */
    public ImageToLatexProcessResult processBatchColumnsWithInsert(String wikiUrl, String[] columns) {
        log.info("开始批量处理多列图片转LaTeX: {}, 列: {}", wikiUrl, String.join(",", columns));
        LocalDateTime startTime = LocalDateTime.now();

        // 第一步：解析并排序列（从右到左处理，避免索引偏移）
        log.info("第一步：解析列参数并排序");
        List<ColumnInfo> columnInfos = new ArrayList<>();

        for (String column : columns) {
            try {
                int columnIndex = ImagePosition.getColumnIndex(column);
                String columnName = ImagePosition.getColumnName(columnIndex);
                columnInfos.add(new ColumnInfo(column, columnIndex, columnName));
                log.debug("解析列: {} -> 索引: {}, 名称: {}", column, columnIndex, columnName);
            } catch (Exception e) {
                log.error("解析列参数失败: {}", column, e);
                throw new AlertsException("无效的列参数: " + column + ", " + e.getMessage());
            }
        }

        // 按列索引从大到小排序（从右到左处理）
        columnInfos.sort((a, b) -> Integer.compare(b.getColumnIndex(), a.getColumnIndex()));
        log.info("列处理顺序（从右到左）: {}",
                columnInfos.stream().map(ColumnInfo::getOriginalInput).collect(Collectors.joining(" -> ")));

        // 第二步：获取基础信息
        log.info("第二步：获取表格基础信息");
        String wikiToken = extractTokenFromWikiUrl(wikiUrl);
        String spreadsheetToken = getSpreadsheetTokenFromWiki(wikiToken);
        List<String> sheetIds = getSheetIdFromWikiUrl(wikiUrl);

        if (sheetIds.isEmpty()) {
            throw new AlertsException("没有找到工作表");
        }

        String sheetId = sheetIds.get(0);
        log.info("使用工作表: {}", sheetId);

        // 第三步：逐列处理（从右到左）
        log.info("第三步：开始逐列处理");
        List<ColumnInsertPlan> allPlans = new ArrayList<>();
        int totalSuccessCount = 0;
        int totalFailCount = 0;

        for (ColumnInfo columnInfo : columnInfos) {
            log.info("处理列: {} (索引: {})", columnInfo.getColumnName(), columnInfo.getColumnIndex());

            // 读取指定列数据
            String range = parseColumnToRange(sheetId, columnInfo.getOriginalInput());
            Object[][] data = readSheetRangeData(spreadsheetToken, range);
            log.info("读取到{}列数据: {} 行", columnInfo.getColumnName(), data.length);

            // 处理单列图片并插入LaTeX
            ImageToLatexProcessResult singleResult = processImagesAndInsertLatex(
                    spreadsheetToken, sheetId, data, columnInfo.getColumnIndex());

            // 累计统计
            if (singleResult.isSuccess() && !singleResult.getColumnInsertPlans().isEmpty()) {
                ColumnInsertPlan plan = singleResult.getColumnInsertPlans().get(0);
                allPlans.add(plan);
                totalSuccessCount += plan.getSuccessfulTaskCount();
                totalFailCount += plan.getFailedTaskCount();
                log.info("列 {} 处理完成: 成功 {}, 失败 {}",
                        columnInfo.getColumnName(), plan.getSuccessfulTaskCount(), plan.getFailedTaskCount());
            }
        }

        // 第四步：创建最终结果
        ImageToLatexProcessResult result = ImageToLatexProcessResult.success(allPlans);
        result.setStartTime(startTime);
        result.setEndTime(LocalDateTime.now());
        result.calculateTotalDuration();

        log.info("批量处理完成: 总成功 {}, 总失败 {}, 处理列数 {}",
                totalSuccessCount, totalFailCount, columns.length);
        return result;
    }

    /**
     * 测试方法：将指定列的图片解析然后在下一列插入对应的LaTeX表达式
     *
     * @param wikiUrl 飞书Wiki链接
     * @param column  要提取图片的列（支持列名如"A","B"或列索引如"0","1"）
     */
    public void test(String wikiUrl, String column) {
        log.info("开始测试图片转LaTeX功能: {}, 目标列: {}", wikiUrl, column);

        try {
            // 第一步：获取基础信息
            log.info("第一步：提取Wiki信息");
            String wikiToken = extractTokenFromWikiUrl(wikiUrl);
            log.info("提取到Wiki Token: {}", wikiToken);

            String spreadsheetToken = getSpreadsheetTokenFromWiki(wikiToken);
            log.info("获取到Spreadsheet Token: {}", spreadsheetToken);

            List<String> sheetIds = getSheetIdFromWikiUrl(wikiUrl);
            log.info("获取到Sheet IDs: {}", sheetIds);

            if (sheetIds.isEmpty()) {
                log.error("没有找到工作表，测试终止");
                return;
            }

            String sheetId = sheetIds.get(0);
            log.info("使用第一个工作表进行测试: {}", sheetId);

            // 第二步：读取指定列数据
            log.info("第二步：读取{}列数据", column);
            String range = parseColumnToRange(sheetId, column);
            Object[][] data = readSheetRangeData(spreadsheetToken, range);
            log.info("读取到{}列数据: {} 行", column, data.length);

            // 打印列数据预览
            String displayColumn = column.matches("^\\d+$") ?
                                   ImagePosition.getColumnName(Integer.parseInt(column)) : column.toUpperCase();
            log.info("{}列数据预览（前5行）:", displayColumn);
            for (int i = 0; i < Math.min(5, data.length); i++) {
                if (data[i].length > 0) {
                    log.info("  {}{}: {}", displayColumn, i + 1, data[i][0]);
                }
            }

            // 第三步：处理图片并插入LaTeX表达式
            log.info("第三步：处理图片并插入LaTeX表达式");
            int sourceColumnIndex = ImagePosition.getColumnIndex(column);
            ImageToLatexProcessResult result = processImagesAndInsertLatex(spreadsheetToken, sheetId, data, sourceColumnIndex);

            // 第四步：输出测试结果
            log.info("第四步：测试结果统计");
            log.info("=== 测试完成 ===");
            log.info("处理结果: {}", result.getSummary());
            log.info("成功状态: {}", result.isSuccess());
            log.info("发现图片数量: {}", result.getTotalImages());
            log.info("转换成功数量: {}", result.getSuccessfulConversions());
            log.info("转换失败数量: {}", result.getFailedConversions());
            log.info("插入列数: {}", result.getInsertedColumns());
            log.info("插入LaTeX表达式数量: {}", result.getInsertedLatexExpressions());
            log.info("总耗时: {} ms", result.getTotalDurationMs());

            // 详细的列插入计划信息
            if (result.getColumnInsertPlans() != null && !result.getColumnInsertPlans().isEmpty()) {
                log.info("=== 详细处理信息 ===");
                for (ColumnInsertPlan plan : result.getColumnInsertPlans()) {
                    log.info("列插入计划: {}", plan);
                    log.info("  - 原始列: {} ({})", plan.getOriginalColumn(), plan.getInsertAfterColumnName());
                    log.info("  - 插入范围: {}", plan.getInsertDataRange());
                    log.info("  - 任务数量: {}", plan.getTasks().size());
                    log.info("  - 成功任务: {}", plan.getSuccessfulTaskCount());
                    log.info("  - 失败任务: {}", plan.getFailedTaskCount());
                    log.info("  - 列插入成功: {}", plan.isInsertSuccess());
                    log.info("  - 数据插入成功: {}", plan.isDataInsertSuccess());

                    // 显示每个转换任务的详情
                    for (LatexConversionTask task : plan.getTasks()) {
                        if (task.isConversionSuccess()) {
                            log.info("    ✓ {} -> {}: {}",
                                    task.getSourceCellAddress(),
                                    task.getTargetCellAddress(),
                                    task.getLatexExpression());
                        }
                        else {
                            log.error("    ✗ {} -> {}: {}",
                                    task.getSourceCellAddress(),
                                    task.getTargetCellAddress(),
                                    task.getErrorMessage());
                        }
                    }
                }
            }

            // 错误信息
            if (result.hasErrors()) {
                log.warn("=== 处理过程中的错误 ===");
                if (result.getErrorMessage() != null) {
                    log.error("主要错误: {}", result.getErrorMessage());
                }
                if (result.getDetailErrors() != null) {
                    for (String error : result.getDetailErrors()) {
                        log.error("详细错误: {}", error);
                    }
                }
            }

            log.info("=== 测试完成 ===");
        } catch (Exception e) {
            log.error("测试过程中发生异常", e);
            throw new AlertsException("测试失败: " + e.getMessage());
        }
    }

    /**
     * 读取表格指定范围的数据
     * 基于飞书官方示例代码实现
     *
     * @param spreadsheetToken spreadsheet令牌
     * @param range            范围（如："A:A", "B1:B100", "Sheet1!A:A"）
     * @return 单元格数据的二维数组
     *
     * @throws RuntimeException 当API调用失败时抛出异常
     */
    public Object[][] readSheetRangeData(String spreadsheetToken, String range) {
        log.info("开始读取表格数据, spreadsheetToken: {}, range: {}", spreadsheetToken, range);

        try {
            // 使用飞书SDK的直接HTTP调用方式（基于官方示例）
            RawResponse readResp = feishuClient.get(
                    String.format("/open-apis/sheets/v2/spreadsheets/%s/values/%s", spreadsheetToken, range),
                    null,
                    AccessTokenType.Tenant);

            // 解析响应数据
            SpreadsheetResp spreadsheetResp = Jsons.DEFAULT.fromJson(
                    new String(readResp.getBody()), SpreadsheetResp.class);

            // 处理服务端错误
            if (!spreadsheetResp.success()) {
                String errorMsg = String.format("读取表格数据失败: code:%s, msg:%s, reqId:%s",
                        spreadsheetResp.getCode(), spreadsheetResp.getMsg(), spreadsheetResp.getRequestId());
                log.error(errorMsg);
                throw new AlertsException(errorMsg);
            }

            // 提取数据
            Object[][] values = null;
            if (spreadsheetResp.getData() != null &&
                    spreadsheetResp.getData().getValueRange() != null) {
                values = spreadsheetResp.getData().getValueRange().getValues();
                String actualRange = spreadsheetResp.getData().getValueRange().getRange();
                log.info("成功读取表格数据，实际范围: {}, 行数: {}",
                        actualRange, values != null ? values.length : 0);
            }

            return values != null ? values : new Object[0][0];
        } catch (Exception e) {
            log.error("读取表格数据时发生异常", e);
            throw new AlertsException("读取表格数据失败: " + e.getMessage());
        }
    }

    /**
     * 主要方法：处理表格中的图片并插入LaTeX表达式
     * 优化后的逐张处理模式：每次处理一张图片（下载->转换->写入）
     *
     * @param spreadsheetToken  表格token
     * @param sheetId           工作表ID
     * @param sheetData         表格数据（来自readSheetRangeData）
     * @param sourceColumnIndex 源列索引（用户指定的列）
     * @return 处理结果
     */
    public ImageToLatexProcessResult processImagesAndInsertLatex(String spreadsheetToken, String sheetId, Object[][] sheetData, int sourceColumnIndex) {
        log.info("开始处理表格中的图片并插入LaTeX表达式: spreadsheetToken={}, sheetId={}, dataRows={}, sourceColumn={}",
                spreadsheetToken, sheetId, sheetData != null ? sheetData.length : 0, sourceColumnIndex);

        LocalDateTime startTime = LocalDateTime.now();

        try {
            // 第一步：扫描并识别图片位置
            List<ImagePosition> imagePositions = scanImagePositions(sheetData, sourceColumnIndex);
            log.info("扫描到 {} 个图片位置", imagePositions.size());

            if (imagePositions.isEmpty()) {
                log.info("没有找到图片，处理完成");
                ImageToLatexProcessResult result = ImageToLatexProcessResult.success(new ArrayList<>());
                result.setStartTime(startTime);
                result.setEndTime(LocalDateTime.now());
                result.calculateTotalDuration();
                return result;
            }

            // 第二步：确定源列和目标列
            String sourceColumnName = ImagePosition.getColumnName(sourceColumnIndex);
            int targetColumnIndex = sourceColumnIndex + 1;
            String targetColumnName = ImagePosition.getColumnName(targetColumnIndex);

            log.info("源列: {} ({}), 目标列: {} ({})", sourceColumnName, sourceColumnIndex, targetColumnName, targetColumnIndex);

            // 第三步：在源列后插入一列（一次性操作）
            log.info("在{}列后插入新列，LaTeX将写入{}列", sourceColumnName, targetColumnName);
            sheetOperationService.insertColumn(spreadsheetToken, sheetId, sourceColumnIndex);
            log.info("列插入成功");

            // 第四步：逐个处理图片（下载->转换->写入）
            List<LatexConversionTask> conversionTasks = new ArrayList<>();
            int successCount = 0;
            int failCount = 0;

            for (int i = 0; i < imagePositions.size(); i++) {
                ImagePosition imagePosition = imagePositions.get(i);
                log.info("处理第{}/{}张图片: {}", i + 1, imagePositions.size(), imagePosition.getCellAddress());

                try {
                    LatexConversionTask task = processSingleImageToLatex(imagePosition, spreadsheetToken, sheetId, targetColumnIndex);
                    conversionTasks.add(task);

                    if (task.isConversionSuccess()) {
                        successCount++;
                        log.info("✓ 图片处理成功: {} -> {}", imagePosition.getCellAddress(), task.getTargetCellAddress());
                    }
                    else {
                        failCount++;
                        log.error("✗ 图片处理失败: {} - {}", imagePosition.getCellAddress(), task.getErrorMessage());
                    }
                } catch (Exception e) {
                    failCount++;
                    log.error("处理图片时发生异常: {}", imagePosition.getCellAddress(), e);
                    LatexConversionTask failedTask = LatexConversionTask.failure(imagePosition, null, e.getMessage(), 0);
                    conversionTasks.add(failedTask);
                }
            }

            // 第五步：创建简化的列插入计划（保持兼容性）
            ColumnInsertPlan plan = ColumnInsertPlan.builder()
                                                    .originalColumn(sourceColumnIndex)
                                                    .insertAfterColumn(sourceColumnIndex)
                                                    .insertAfterColumnName(sourceColumnName)
                                                    .tasks(conversionTasks)
                                                    .insertSuccess(true)
                                                    .dataInsertSuccess(successCount > 0)
                                                    .build();

            List<ColumnInsertPlan> insertPlans = List.of(plan);

            // 创建成功结果
            ImageToLatexProcessResult result = ImageToLatexProcessResult.success(insertPlans);
            result.setStartTime(startTime);
            result.setEndTime(LocalDateTime.now());
            result.calculateTotalDuration();

            log.info("图片转LaTeX处理完成: {}", result.getSummary());
            log.info("成功: {}, 失败: {}", successCount, failCount);
            return result;
        } catch (Exception e) {
            log.error("处理图片转LaTeX时发生异常", e);
            ImageToLatexProcessResult result = ImageToLatexProcessResult.failure(e.getMessage(), List.of(e.toString()));
            result.setStartTime(startTime);
            result.setEndTime(LocalDateTime.now());
            result.calculateTotalDuration();
            return result;
        }
    }

    /**
     * 按指定单元格坐标处理图片转LaTeX（新功能）
     * 不插入新列，直接在右边单元格写入LaTeX结果
     *
     * @param wikiUrl       飞书Wiki链接
     * @param cellAddresses 单元格地址数组（如：["A1", "A2", "E1"]）
     * @return 处理结果
     */
    public ImageToLatexProcessResult processImagesByCellAddresses(String wikiUrl, String[] cellAddresses) {
        log.info("开始按坐标处理图片转LaTeX: {}, 坐标数量: {}", wikiUrl, cellAddresses.length);
        log.info("处理坐标列表: {}", String.join(", ", cellAddresses));

        LocalDateTime startTime = LocalDateTime.now();

        // 第一步：获取基础信息
        log.info("第一步：提取Wiki信息");
        String wikiToken = extractTokenFromWikiUrl(wikiUrl);
        log.info("提取到Wiki Token: {}", wikiToken);

        String spreadsheetToken = getSpreadsheetTokenFromWiki(wikiToken);
        log.info("获取到Spreadsheet Token: {}", spreadsheetToken);

        List<String> sheetIds = getSheetIdFromWikiUrl(wikiUrl);
        log.info("获取到Sheet IDs: {}", sheetIds);

        if (sheetIds.isEmpty()) {
            log.error("没有找到工作表，处理终止");
            ImageToLatexProcessResult result = ImageToLatexProcessResult.failure("没有找到工作表", List.of("工作表列表为空"));
            result.setStartTime(startTime);
            result.setEndTime(LocalDateTime.now());
            result.calculateTotalDuration();
            return result;
        }

        // 使用第一个工作表
        String sheetId = sheetIds.get(0);
        log.info("使用工作表: {}", sheetId);

        // 第二步：解析坐标并读取数据
        log.info("第二步：解析坐标并读取单元格数据");
        List<ImagePosition> imagePositions = new ArrayList<>();

        for (String cellAddress : cellAddresses) {
            try {
                log.debug("处理坐标: {}", cellAddress);

                // 解析坐标
                ImagePosition position = parseCellAddress(cellAddress);
                log.debug("解析坐标 {} -> 行:{}, 列:{}", cellAddress, position.getRow(), position.getColumn());

                // 读取单元格数据
                Object cellData = readSingleCellData(spreadsheetToken, sheetId, cellAddress);
                log.debug("读取到单元格 {} 的数据: {}", cellAddress, cellData);

                // 提取图片token
                String cellContent = cellData != null ? cellData.toString() : "";
                String fileToken = extractFileToken(cellContent);

                if (fileToken != null) {
                    // 更新ImagePosition的数据
                    position.setFileToken(fileToken);
                    position.setOriginalData(cellData);
                    imagePositions.add(position);
                    log.info("✓ 发现图片: {} -> token: {}", cellAddress, fileToken);
                }
                else {
                    log.debug("✗ 单元格 {} 不包含图片: '{}'", cellAddress, cellContent);
                }
            } catch (Exception e) {
                log.error("处理坐标 {} 时发生异常", cellAddress, e);
            }
        }

        log.info("坐标解析完成，发现 {} 个图片", imagePositions.size());

        if (imagePositions.isEmpty()) {
            log.info("没有找到图片，处理完成");
            ImageToLatexProcessResult result = ImageToLatexProcessResult.success(new ArrayList<>());
            result.setStartTime(startTime);
            result.setEndTime(LocalDateTime.now());
            result.calculateTotalDuration();
            return result;
        }

        // 第三步：逐个处理图片（下载->转换->写入到右边单元格）
        log.info("第三步：逐个处理图片转LaTeX");
        List<LatexConversionTask> conversionTasks = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        for (int i = 0; i < imagePositions.size(); i++) {
            ImagePosition imagePosition = imagePositions.get(i);
            log.info("处理第{}/{}张图片: {}", i + 1, imagePositions.size(), imagePosition.getCellAddress());

            try {
                LatexConversionTask task = processSingleImageToCellAddress(imagePosition, spreadsheetToken, sheetId);
                conversionTasks.add(task);

                if (task.isConversionSuccess()) {
                    successCount++;
                    log.info("✓ 图片处理成功: {} -> {}", imagePosition.getCellAddress(), task.getTargetCellAddress());
                }
                else {
                    failCount++;
                    log.error("✗ 图片处理失败: {} - {}", imagePosition.getCellAddress(), task.getErrorMessage());
                }
            } catch (Exception e) {
                failCount++;
                log.error("处理图片时发生异常: {}", imagePosition.getCellAddress(), e);
                LatexConversionTask failedTask = LatexConversionTask.failure(imagePosition, null, e.getMessage(), 0);
                conversionTasks.add(failedTask);
            }
        }

        // 第四步：创建处理结果
        log.info("第四步：创建处理结果");
        ColumnInsertPlan plan = ColumnInsertPlan.builder()
                                                .originalColumn(-1) // 不适用于坐标模式
                                                .insertAfterColumn(-1) // 不插入列
                                                .insertAfterColumnName("N/A") // 不插入列
                                                .tasks(conversionTasks)
                                                .insertSuccess(true) // 不需要插入列，直接标记成功
                                                .dataInsertSuccess(successCount > 0)
                                                .build();

        List<ColumnInsertPlan> insertPlans = List.of(plan);

        // 创建成功结果
        ImageToLatexProcessResult result = ImageToLatexProcessResult.success(insertPlans);
        result.setStartTime(startTime);
        result.setEndTime(LocalDateTime.now());
        result.calculateTotalDuration();

        log.info("按坐标处理图片转LaTeX完成: {}", result.getSummary());
        log.info("成功: {}, 失败: {}", successCount, failCount);
        return result;
    }

    /**
     * 解析单元格地址为ImagePosition对象
     *
     * @param cellAddress 单元格地址（如："A1", "E1", "AA10"）
     * @return ImagePosition对象
     *
     * @throws IllegalArgumentException 当地址格式无效时抛出异常
     */
    ImagePosition parseCellAddress(String cellAddress) {
        if (cellAddress == null || cellAddress.trim().isEmpty()) {
            throw new IllegalArgumentException("单元格地址不能为空");
        }

        String upperCellAddress = cellAddress.trim().toUpperCase();
        log.debug("解析单元格地址: {}", upperCellAddress);

        // 使用正则表达式分离列名和行号
        // 支持格式：A1, B2, AA10, AB100等
        Pattern pattern = Pattern.compile("^([A-Z]+)(\\d+)$");
        Matcher matcher = pattern.matcher(upperCellAddress);

        if (!matcher.find()) {
            throw new IllegalArgumentException("无效的单元格地址格式: " + cellAddress);
        }

        String columnName = matcher.group(1);
        String rowString = matcher.group(2);

        try {
            // 转换列名为列索引（0-based）
            int columnIndex = ImagePosition.getColumnIndex(columnName);

            // 转换行号为行索引（0-based）
            int rowIndex = Integer.parseInt(rowString) - 1;

            if (rowIndex < 0) {
                throw new IllegalArgumentException("行号必须大于0: " + rowString);
            }

            // 创建ImagePosition对象
            ImagePosition position = ImagePosition.create(rowIndex, columnIndex, null, null);
            log.debug("解析结果: {} -> 行:{}, 列:{}", cellAddress, rowIndex, columnIndex);

            return position;
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无效的行号: " + rowString, e);
        } catch (Exception e) {
            throw new IllegalArgumentException("解析单元格地址失败: " + cellAddress, e);
        }
    }

    /**
     * 解析列参数并构建范围字符串
     *
     * @param sheetId 工作表ID
     * @param column  列参数（列名或列索引）
     * @return 范围字符串（如"sheetId!A:A"）
     */
    private String parseColumnToRange(String sheetId, String column) {
        try {
            int columnIndex = ImagePosition.getColumnIndex(column);
            String columnName = ImagePosition.getColumnName(columnIndex);
            return sheetId + "!" + columnName + ":" + columnName;
        } catch (Exception e) {
            log.error("解析列参数失败: {}", column, e);
            throw new AlertsException("无效的列参数: " + column + ", " + e.getMessage());
        }
    }

    /**
     * 从单元格内容中提取文件token
     * 支持多种格式的文件token识别
     */
    private String extractFileToken(String cellContent) {
        if (cellContent == null || cellContent.isEmpty()) {
            return null;
        }

        String trimmedContent = cellContent.trim();
        log.debug("正在提取文件token，原始内容: '{}'", trimmedContent);

        // 1. 匹配飞书文件token格式（以box开头）
        Pattern boxTokenPattern = Pattern.compile("box[a-zA-Z0-9]{15,}");
        Matcher boxMatcher = boxTokenPattern.matcher(trimmedContent);
        if (boxMatcher.find()) {
            String token = boxMatcher.group();
            log.debug("找到box格式token: {}", token);
            return token;
        }

        // 2. 匹配其他飞书文件token格式（如WOPI开头等）
        Pattern generalTokenPattern = Pattern.compile("[a-zA-Z0-9]{20,}");
        Matcher generalMatcher = generalTokenPattern.matcher(trimmedContent);
        if (generalMatcher.find()) {
            String token = generalMatcher.group();
            // 验证是否为有效的文件token（长度和字符组成）
            if (isValidFileToken(token)) {
                log.debug("找到通用格式token: {}", token);
                return token;
            }
        }

        // 3. 如果整个内容就是token格式
        if (isValidFileToken(trimmedContent)) {
            log.debug("整个内容为token: {}", trimmedContent);
            return trimmedContent;
        }

        log.debug("未找到有效的文件token");
        return null;
    }

    /**
     * 验证是否为有效的文件token
     */
    private boolean isValidFileToken(String token) {
        if (token == null || token.length() < 15) {
            return false;
        }

        // 检查是否只包含字母和数字
        if (!token.matches("^[a-zA-Z0-9]+$")) {
            return false;
        }

        // 排除一些明显不是token的内容
        String lowerToken = token.toLowerCase();
        // 过长
        return !lowerToken.equals("null") &&
                !lowerToken.equals("undefined") &&
                !lowerToken.matches("^\\d+$") && // 纯数字
                lowerToken.length() <= 100;
    }

    /**
     * 从飞书下载图片
     */
    private byte[] downloadImageFromFeishu(String fileToken) throws Exception {
        try {
            // 创建下载请求
            DownloadMediaReq req = DownloadMediaReq.newBuilder()
                                                   .fileToken(fileToken)
                                                   .extra("") // 可选参数
                                                   .build();

            // 发起下载请求
            DownloadMediaResp resp = feishuClient.drive().v1().media().download(req);

            // 检查响应
            if (!resp.success()) {
                String errorMsg = String.format("飞书API下载失败: code:%s, msg:%s, reqId:%s",
                        resp.getCode(), resp.getMsg(), resp.getRequestId());
                throw new RuntimeException(errorMsg);
            }

            // 获取文件数据
            byte[] fileData = resp.getRawResponse().getBody();
            if (fileData == null || fileData.length == 0) {
                throw new RuntimeException("下载的文件数据为空");
            }

            return fileData;
        } catch (Exception e) {
            throw new Exception("调用飞书下载API失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理单张图片：下载 -> 转换 -> 写入
     *
     * @param imagePosition     图片位置信息
     * @param spreadsheetToken  表格token
     * @param sheetId           工作表ID
     * @param targetColumnIndex 目标列索引
     * @return 处理结果
     */
    private LatexConversionTask processSingleImageToLatex(ImagePosition imagePosition, String spreadsheetToken, String sheetId, int targetColumnIndex) {
        LocalDateTime startTime = LocalDateTime.now();
        String fileToken = imagePosition.getFileToken();

        try {
            log.debug("开始处理单张图片: {}", imagePosition);

            // 第一步：下载图片数据
            byte[] imageData = downloadImageFromFeishu(fileToken);
            log.debug("图片下载成功，大小: {} bytes", imageData.length);

            // 第二步：转换为LaTeX
            String fileName = "image_" + fileToken.substring(Math.max(0, fileToken.length() - 8));
            String latexExpression = imageToLatexConverter.convertImageToLatex(imageData, fileName);
            log.debug("LaTeX转换成功: {}", latexExpression);

            // 第三步：计算目标位置并写入
            String targetColumnName = ImagePosition.getColumnName(targetColumnIndex);
            String targetCellAddress = targetColumnName + (imagePosition.getRow() + 1);
            String range = sheetId + "!" + targetCellAddress + ":" + targetCellAddress;

            // 写入LaTeX表达式到目标位置
            Object[][] data = {{latexExpression}};
            sheetOperationService.updateDataInRange(spreadsheetToken, sheetId, range, data);
            log.debug("LaTeX写入成功: {} -> {}", imagePosition.getCellAddress(), targetCellAddress);

            // 创建成功的转换任务
            LatexConversionTask task = LatexConversionTask.success(imagePosition, imageData, latexExpression);
            task.setStartTime(startTime);
            task.setEndTime(LocalDateTime.now());
            task.calculateDuration();

            return task;
        } catch (Exception e) {
            log.error("处理单张图片失败: {}", imagePosition, e);
            LatexConversionTask task = LatexConversionTask.failure(imagePosition, null, e.getMessage(), 0);
            task.setStartTime(startTime);
            task.setEndTime(LocalDateTime.now());
            task.calculateDuration();
            return task;
        }
    }

    /**
     * 扫描表格数据中的图片位置
     *
     * @param sheetData         表格数据
     * @param sourceColumnIndex 源列索引（当读取单列数据时，需要指定实际的列索引）
     * @return 图片位置列表
     */
    private List<ImagePosition> scanImagePositions(Object[][] sheetData, int sourceColumnIndex) {
        List<ImagePosition> positions = new ArrayList<>();

        if (sheetData == null) {
            log.warn("表格数据为null，无法扫描图片位置");
            return positions;
        }

        log.info("开始扫描表格数据中的图片位置，数据行数: {}", sheetData.length);

        for (int row = 0; row < sheetData.length; row++) {
            Object[] rowData = sheetData[row];
            if (rowData == null) {
                log.debug("第{}行数据为null，跳过", row + 1);
                continue;
            }

            for (int col = 0; col < rowData.length; col++) {
                Object cell = rowData[col];
                String cellContent = cell != null ? cell.toString() : "";

                // 计算实际的列索引：如果是单列数据，使用sourceColumnIndex；否则使用col
                int actualColumnIndex = (rowData.length == 1) ? sourceColumnIndex : col;

                log.debug("扫描单元格 {}{}，内容: '{}'",
                        ImagePosition.getColumnName(actualColumnIndex), row + 1, cellContent);

                String fileToken = extractFileToken(cellContent);

                if (fileToken != null) {
                    ImagePosition position = ImagePosition.create(row, actualColumnIndex, fileToken, cell);
                    positions.add(position);
                    log.info("✓ 发现图片: {} -> token: {}", position.getCellAddress(), fileToken);
                }
                else if (!cellContent.trim().isEmpty()) {
                    log.debug("✗ 单元格 {}{} 内容不是图片token: '{}'",
                            ImagePosition.getColumnName(actualColumnIndex), row + 1, cellContent);
                }
            }
        }

        log.info("图片位置扫描完成，共发现 {} 个图片", positions.size());
        return positions;
    }

    /**
     * 读取单个单元格的数据
     *
     * @param spreadsheetToken 表格token
     * @param sheetId          工作表ID
     * @param cellAddress      单元格地址（如："A1"）
     * @return 单元格数据，如果为空则返回null
     *
     * @throws Exception 读取失败时抛出异常
     */
    private Object readSingleCellData(String spreadsheetToken, String sheetId, String cellAddress) throws Exception {
        log.debug("读取单元格数据: {}", cellAddress);

        try {
            // 构建范围字符串：sheetId!cellAddress
            String range = sheetId + "!" + cellAddress + ":" + cellAddress;

            // 使用现有的读取方法
            Object[][] data = readSheetRangeData(spreadsheetToken, range);

            // 检查返回的数据
            if (data == null || data.length == 0) {
                log.debug("单元格 {} 为空或不存在", cellAddress);
                return null;
            }

            if (data[0] == null || data[0].length == 0) {
                log.debug("单元格 {} 数据为空", cellAddress);
                return null;
            }

            Object cellData = data[0][0];
            log.debug("读取到单元格 {} 的数据: {}", cellAddress, cellData);
            return cellData;
        } catch (Exception e) {
            log.error("读取单元格 {} 数据时发生异常", cellAddress, e);
            throw new Exception("读取单元格数据失败: " + cellAddress, e);
        }
    }

    /**
     * 处理单张图片并写入到指定的目标单元格（不插入新列）
     *
     * @param imagePosition    图片位置信息
     * @param spreadsheetToken 表格token
     * @param sheetId          工作表ID
     * @return 处理结果
     */
    private LatexConversionTask processSingleImageToCellAddress(ImagePosition imagePosition, String spreadsheetToken, String sheetId) {
        LocalDateTime startTime = LocalDateTime.now();
        String fileToken = imagePosition.getFileToken();

        try {
            log.debug("开始处理单张图片到指定位置: {}", imagePosition);

            // 第一步：下载图片数据
            byte[] imageData = downloadImageFromFeishu(fileToken);
            log.debug("图片下载成功，大小: {} bytes", imageData.length);

            // 第二步：转换为LaTeX
            String fileName = "image_" + fileToken.substring(Math.max(0, fileToken.length() - 8));
            String latexExpression = imageToLatexConverter.convertImageToLatex(imageData, fileName);
            log.debug("LaTeX转换成功: {}", latexExpression);

            // 第三步：计算目标位置（源位置的右边一列）
            int targetColumnIndex = imagePosition.getColumn() + 1;
            String targetColumnName = ImagePosition.getColumnName(targetColumnIndex);
            String targetCellAddress = targetColumnName + (imagePosition.getRow() + 1);
            String range = sheetId + "!" + targetCellAddress + ":" + targetCellAddress;

            log.debug("目标位置: {} -> {}", imagePosition.getCellAddress(), targetCellAddress);

            // 第四步：写入LaTeX表达式到目标位置
            Object[][] data = {{latexExpression}};
            sheetOperationService.updateDataInRange(spreadsheetToken, sheetId, range, data);
            log.debug("LaTeX写入成功: {} -> {}", imagePosition.getCellAddress(), targetCellAddress);

            // 创建成功的转换任务
            LatexConversionTask task = LatexConversionTask.success(imagePosition, imageData, latexExpression);
            task.setTargetCellAddress(targetCellAddress); // 设置目标单元格地址
            task.setStartTime(startTime);
            task.setEndTime(LocalDateTime.now());
            task.calculateDuration();

            return task;
        } catch (Exception e) {
            log.error("处理单张图片到指定位置失败: {}", imagePosition, e);
            LatexConversionTask task = LatexConversionTask.failure(imagePosition, null, e.getMessage(), 0);
            task.setStartTime(startTime);
            task.setEndTime(LocalDateTime.now());
            task.calculateDuration();
            return task;
        }
    }

    /**
     * 列信息内部类
     * 用于存储列的原始输入、索引和名称信息
     */
    private static class ColumnInfo {

        private final String originalInput;

        private final int columnIndex;

        private final String columnName;

        public ColumnInfo(String originalInput, int columnIndex, String columnName) {
            this.originalInput = originalInput;
            this.columnIndex = columnIndex;
            this.columnName = columnName;
        }

        public String getOriginalInput() {
            return originalInput;
        }

        public int getColumnIndex() {
            return columnIndex;
        }

        public String getColumnName() {
            return columnName;
        }
    }
}