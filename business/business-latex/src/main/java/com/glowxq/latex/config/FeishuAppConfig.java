package com.glowxq.latex.config;

import com.lark.oapi.Client;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 飞书配置类
 *
 * <AUTHOR>
 * @date 2025/01/22
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "feishu.app")
public class FeishuAppConfig {

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * 创建飞书客户端Bean
     */
    @Bean
    public Client feishuClient() {
        return Client.newBuilder(appId, appSecret)
                .build();
    }
}
