package com.glowxq.latex.service;

/**
 * 表格操作服务接口
 * 定义了对飞书表格进行操作的标准接口
 * 
 * <AUTHOR>
 * @date 2025/01/23
 */
public interface SheetOperationService {

    /**
     * 在指定位置插入列
     * 
     * @param spreadsheetToken 表格token
     * @param sheetId 工作表ID
     * @param afterColumn 在此列后插入新列（0-based）
     * @throws Exception 插入失败时抛出异常
     */
    void insertColumn(String spreadsheetToken, String sheetId, int afterColumn) throws Exception;

    /**
     * 批量插入多列
     * 
     * @param spreadsheetToken 表格token
     * @param sheetId 工作表ID
     * @param afterColumn 在此列后插入新列（0-based）
     * @param columnCount 插入的列数
     * @throws Exception 插入失败时抛出异常
     */
    void insertColumns(String spreadsheetToken, String sheetId, int afterColumn, int columnCount) throws Exception;

    /**
     * 在指定范围插入数据
     * 
     * @param spreadsheetToken 表格token
     * @param sheetId 工作表ID
     * @param range 插入范围（如：B1:B10）
     * @param data 要插入的数据（二维数组）
     * @throws Exception 插入失败时抛出异常
     */
    void insertDataToRange(String spreadsheetToken, String sheetId, String range, Object[][] data) throws Exception;

    /**
     * 更新指定范围的数据
     * 
     * @param spreadsheetToken 表格token
     * @param sheetId 工作表ID
     * @param range 更新范围（如：B1:B10）
     * @param data 要更新的数据（二维数组）
     * @throws Exception 更新失败时抛出异常
     */
    void updateDataInRange(String spreadsheetToken, String sheetId, String range, Object[][] data) throws Exception;

    /**
     * 检查表格操作服务是否可用
     * 
     * @return true表示服务可用，false表示不可用
     */
    boolean isServiceAvailable();

    /**
     * 获取服务名称
     * 
     * @return 服务名称
     */
    String getServiceName();

    /**
     * 验证表格和工作表是否存在
     * 
     * @param spreadsheetToken 表格token
     * @param sheetId 工作表ID
     * @return true表示存在，false表示不存在
     */
    boolean validateSheetExists(String spreadsheetToken, String sheetId);

    /**
     * 获取表格的基本信息（用于调试）
     * 
     * @param spreadsheetToken 表格token
     * @param sheetId 工作表ID
     * @return 表格信息字符串
     */
    String getSheetInfo(String spreadsheetToken, String sheetId);
}
