package com.glowxq.latex.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图片处理任务DTO
 * 
 * <AUTHOR>
 * @date 2025/01/23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImageProcessTask {

    /**
     * 文件token（飞书文件标识）
     */
    private String fileToken;

    /**
     * 目标下载目录
     */
    private String downloadDir;

    /**
     * 任务索引（用于排序和日志）
     */
    private int taskIndex;

    /**
     * 原始单元格数据（用于调试）
     */
    private Object cellData;

    /**
     * 是否为有效的图片token
     */
    private boolean validToken;

    /**
     * 创建有效任务
     */
    public static ImageProcessTask createValid(String fileToken, String downloadDir, int taskIndex, Object cellData) {
        return ImageProcessTask.builder()
                .fileToken(fileToken)
                .downloadDir(downloadDir)
                .taskIndex(taskIndex)
                .cellData(cellData)
                .validToken(true)
                .build();
    }

    /**
     * 创建无效任务
     */
    public static ImageProcessTask createInvalid(int taskIndex, Object cellData, String downloadDir) {
        return ImageProcessTask.builder()
                .downloadDir(downloadDir)
                .taskIndex(taskIndex)
                .cellData(cellData)
                .validToken(false)
                .build();
    }
}
