package com.glowxq.latex.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.glowxq.core.common.exception.common.AlertsException;
import com.glowxq.latex.dto.ImageToLatexProcessResult;
import com.glowxq.latex.dto.ParseTableByCellsRequest;
import com.glowxq.latex.dto.ParseTableRequest;
import com.glowxq.latex.service.LatexService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * LaTeX控制器
 *
 * <AUTHOR>
 * @date 2025/01/22
 */
@Slf4j
@RestController
@RequestMapping("/latex")
@RequiredArgsConstructor
@Tag(name = "LaTeX管理", description = "LaTeX相关功能")
public class LatexController {

    private final LatexService latexService;

    /**
     * 解析表格数据（使用RequestBody方式）
     *
     * @param request 解析表格请求参数
     * @return 解析结果
     */
    @PostMapping("/parse-table-json")
    @SaIgnore
    @Operation(summary = "解析表格数据", description = "根据表格链接和指定列解析表格数据（使用JSON请求体）")
    public String parseTableJson(@Valid @RequestBody ParseTableRequest request) {

        log.info("开始批量解析表格数据，链接: {}, 解析列: {}",
                request.getTableUrl(), String.join(",", request.getColumns()));

        // 使用新的批量处理方法，从右到左处理避免列索引偏移问题
        ImageToLatexProcessResult result = latexService.processBatchColumnsWithInsert(
                request.getTableUrl(), request.getColumns());

        if (result.isSuccess()) {
            int totalSuccess = result.getColumnInsertPlans().stream()
                                     .mapToInt(plan -> plan.getSuccessfulTaskCount())
                                     .sum();
            int totalFail = result.getColumnInsertPlans().stream()
                                  .mapToInt(plan -> plan.getFailedTaskCount())
                                  .sum();

            log.info("批量解析完成: 成功 {}, 失败 {}", totalSuccess, totalFail);
            return String.format("表格批量解析成功，链接: %s，解析列数: %d，成功转换: %d，失败转换: %d",
                    request.getTableUrl(), request.getColumns().length, totalSuccess, totalFail);
        }
        else {
            log.error("批量解析失败: {}", result.getErrorMessage());
            // 转换失败时抛出AlertsException，触发飞书告警
            throw new AlertsException("表格批量解析失败: " + result.getErrorMessage());
        }
    }

    /**
     * 按单元格坐标解析表格数据
     *
     * @param request 按坐标解析表格请求参数
     * @return 解析结果
     */
    @PostMapping("/parse-table-by-cells")
    @SaIgnore
    @Operation(summary = "按单元格坐标解析表格", description = "根据指定的单元格坐标列表处理图片转LaTeX")
    public String parseTableByCells(@Valid @RequestBody ParseTableByCellsRequest request) {

        log.info("开始按坐标解析表格数据，链接: {}, 坐标列表: {}",
                request.getTableUrl(), String.join(",", request.getCellAddresses()));

        // 调用新的服务方法处理指定坐标的图片转LaTeX
        ImageToLatexProcessResult result = latexService.processImagesByCellAddresses(
                request.getTableUrl(), request.getCellAddresses());

        log.info("按坐标解析表格完成: {}", result.getSummary());
        return String.format("表格按坐标解析成功，链接: %s，处理坐标数: %d，成功转换: %d，失败转换: %d",
                request.getTableUrl(),
                request.getCellAddresses().length,
                result.getSuccessfulConversions(),
                result.getFailedConversions());
    }
}
