package com.glowxq.latex.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 图片下载结果DTO
 * 
 * <AUTHOR>
 * @date 2025/01/23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImageDownloadResult {

    /**
     * 文件token（飞书文件标识）
     */
    private String fileToken;

    /**
     * 下载是否成功
     */
    private boolean success;

    /**
     * 下载的图片数据（字节数组）
     */
    private byte[] imageData;

    /**
     * 保存的文件路径
     */
    private String filePath;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件大小（字节）
     */
    private long fileSize;

    /**
     * 错误信息（下载失败时）
     */
    private String errorMessage;

    /**
     * 重试次数
     */
    private int retryCount;

    /**
     * 下载开始时间
     */
    private LocalDateTime startTime;

    /**
     * 下载完成时间
     */
    private LocalDateTime endTime;

    /**
     * 下载耗时（毫秒）
     */
    private long durationMs;

    /**
     * 创建成功结果
     */
    public static ImageDownloadResult success(String fileToken, byte[] imageData, String filePath, String fileName) {
        return ImageDownloadResult.builder()
                .fileToken(fileToken)
                .success(true)
                .imageData(imageData)
                .filePath(filePath)
                .fileName(fileName)
                .fileSize(imageData != null ? imageData.length : 0)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果
     */
    public static ImageDownloadResult failure(String fileToken, String errorMessage, int retryCount) {
        return ImageDownloadResult.builder()
                .fileToken(fileToken)
                .success(false)
                .errorMessage(errorMessage)
                .retryCount(retryCount)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 计算下载耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.durationMs = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }
}
