# 飞书Client官方方法回滚说明

## 🔄 回滚原因

根据您的要求，我们回滚了之前使用标准HTTP客户端的代码，改为使用飞书官方提供的Client方法。

## 📋 修改内容

### 1. 回滚的更改
- ❌ 移除了`RestTemplate`的使用
- ❌ 移除了手动构建HTTP请求头和认证
- ❌ 移除了`getAccessToken()`辅助方法
- ✅ 恢复使用飞书官方`Client`的`get()`, `post()`, `put()`方法

### 2. 关键修改点

#### 修改前（标准HTTP客户端）：
```java
String accessToken = getAccessToken();
HttpHeaders headers = new HttpHeaders();
headers.setBearerAuth(accessToken);
ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);
```

#### 修改后（官方Client方法）：
```java
RawResponse response = feishuClient.post(url, requestBody, AccessTokenType.Tenant);
```

### 3. 核心改进

#### A. 直接传入Map对象
**关键变化**：不再将请求体转换为JSON字符串，而是直接传入Map对象
```java
// 之前可能的问题写法
String requestBodyJson = Jsons.DEFAULT.toJson(requestBody);
RawResponse response = feishuClient.post(url, requestBodyJson, AccessTokenType.Tenant);

// 现在的正确写法
RawResponse response = feishuClient.post(url, requestBody, AccessTokenType.Tenant);
```

#### B. 统一的错误处理
所有方法都使用相同的响应解析和错误处理逻辑：
```java
String responseBody = new String(response.getBody(), StandardCharsets.UTF_8);
Map<String, Object> responseMap = Jsons.DEFAULT.fromJson(responseBody, Map.class);
Integer code = (Integer) responseMap.get("code");

if (code == null || code != 0) {
    String msg = (String) responseMap.get("msg");
    throw new RuntimeException("操作失败: code=" + code + ", msg=" + msg);
}
```

## 🔧 修改的方法

### 1. insertColumns() - 插入列
```java
@Override
public void insertColumns(String spreadsheetToken, String sheetId, int afterColumn, int columnCount) throws Exception {
    Map<String, Object> requestBody = new HashMap<>();
    requestBody.put("major_dimension", "COLUMNS");
    requestBody.put("start_index", afterColumn + 1);
    requestBody.put("length", columnCount);

    RawResponse response = feishuClient.post(
        String.format("/open-apis/sheets/v3/spreadsheets/%s/sheets/%s/insert_rows_or_columns", 
                spreadsheetToken, sheetId),
        requestBody,  // 直接传入Map对象
        AccessTokenType.Tenant);
    
    // 统一的响应处理...
}
```

### 2. insertDataToRange() - 插入数据
```java
@Override
public void insertDataToRange(String spreadsheetToken, String sheetId, String range, Object[][] data) throws Exception {
    Map<String, Object> requestBody = new HashMap<>();
    Map<String, Object> valueRange = new HashMap<>();
    valueRange.put("range", range);
    valueRange.put("values", data);
    requestBody.put("valueRange", valueRange);

    RawResponse response = feishuClient.post(
        String.format("/open-apis/sheets/v3/spreadsheets/%s/values/prepend", spreadsheetToken),
        requestBody,  // 直接传入Map对象
        AccessTokenType.Tenant);
    
    // 统一的响应处理...
}
```

### 3. updateDataInRange() - 更新数据
```java
@Override
public void updateDataInRange(String spreadsheetToken, String sheetId, String range, Object[][] data) throws Exception {
    Map<String, Object> requestBody = new HashMap<>();
    Map<String, Object> valueRange = new HashMap<>();
    valueRange.put("range", range);
    valueRange.put("values", data);
    requestBody.put("valueRange", valueRange);

    RawResponse response = feishuClient.put(
        String.format("/open-apis/sheets/v3/spreadsheets/%s/values/%s", spreadsheetToken, range),
        requestBody,  // 直接传入Map对象
        AccessTokenType.Tenant);
    
    // 统一的响应处理...
}
```

### 4. validateSheetExists() - 验证工作表
```java
@Override
public boolean validateSheetExists(String spreadsheetToken, String sheetId) {
    try {
        RawResponse response = feishuClient.get(
            String.format("/open-apis/sheets/v3/spreadsheets/%s/sheets/%s", spreadsheetToken, sheetId),
            null,
            AccessTokenType.Tenant);

        String responseBody = new String(response.getBody(), StandardCharsets.UTF_8);
        Map<String, Object> responseMap = Jsons.DEFAULT.fromJson(responseBody, Map.class);
        Integer code = (Integer) responseMap.get("code");
        
        return code != null && code == 0;
    } catch (Exception e) {
        return false;
    }
}
```

## 🎯 预期解决的问题

### 1. Java模块系统兼容性
通过使用官方Client方法，应该能够避免之前的反射访问问题：
- 官方SDK应该已经处理了Java模块系统的兼容性
- 减少了自定义HTTP客户端可能引入的问题

### 2. API调用一致性
- 使用官方推荐的调用方式
- 保持与飞书SDK的一致性
- 减少维护成本

### 3. 错误处理标准化
- 统一的响应解析逻辑
- 标准化的错误信息格式
- 更好的调试信息

## 🧪 测试验证

### 1. 功能验证
回滚后需要验证以下功能：
- ✅ 插入列功能是否正常
- ✅ 插入数据功能是否正常
- ✅ 更新数据功能是否正常
- ✅ 工作表验证功能是否正常

### 2. 兼容性验证
- ✅ Java 17+环境下是否正常运行
- ✅ 是否还会出现反射访问异常
- ✅ 飞书API调用是否成功

### 3. 错误处理验证
- ✅ 网络异常处理是否正确
- ✅ API错误响应处理是否正确
- ✅ 日志记录是否完整

## 🚀 使用方法

回滚完成后，您可以重新测试：

```bash
# 通过API测试
curl "http://localhost:8080/latex/test-simple?wikiUrl=https://your-wiki-url"

# 或通过单元测试
mvn test -Dtest=LatexServiceTest#testIntegratedImageToLatexFlow
```

## ⚠️ 注意事项

### 1. 如果仍然出现反射问题
可能需要在JVM启动参数中添加：
```bash
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
```

### 2. 监控日志输出
注意观察以下日志：
- 插入列的请求和响应日志
- 插入数据的请求和响应日志
- 任何异常或错误信息

### 3. API版本兼容性
确保使用的飞书API版本与SDK版本兼容：
- 当前使用的是v3版本的sheets API
- 确认SDK版本支持这些API

## 📈 后续优化

如果官方Client方法仍然有问题，可以考虑：

1. **升级飞书SDK版本**
2. **使用不同的API版本**
3. **添加JVM启动参数**
4. **联系飞书技术支持**

回滚完成！现在代码使用的是飞书官方推荐的Client方法，应该能够更好地处理Java模块系统的兼容性问题。
