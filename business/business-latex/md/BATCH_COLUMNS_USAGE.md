# 批量列处理功能使用指南

## 功能概述

新的批量列处理功能解决了多列处理时的列索引偏移问题。当用户指定 `["A","E"]` 时，系统会从右到左处理（E→A），确保每列都能正确处理。

## 问题解决

### 原有问题
```
用户输入: ["A", "E"]
处理顺序: A → E
结果: A列插入后，原E列变成F列，实际处理的是D列
```

### 新方案解决
```
用户输入: ["A", "E"] 
处理顺序: E → A (倒序)
结果: E列先处理不受影响，A列后处理也正确
```

## API使用

### 请求示例

```bash
curl -X POST http://localhost:8080/latex/parse-table-json \
  -H "Content-Type: application/json" \
  -d '{
    "tableUrl": "https://xxx.feishu.cn/wiki/OXaCwMKF1ie5YHkVOhPcYlvlnQc",
    "columns": ["A", "E"]
  }'
```

### 响应示例

**成功响应**：
```
表格批量解析成功，链接: https://xxx.feishu.cn/wiki/OXaCwMKF1ie5YHkVOhPcYlvlnQc，解析列数: 2，成功转换: 5，失败转换: 1
```

**失败响应**：
```
表格批量解析失败: 无效的列参数: X
```

## 处理逻辑

### 1. 列解析和排序
```java
输入: ["A", "C", "E", "B"]
解析: A=0, C=2, E=4, B=1
排序: [E(4), C(2), B(1), A(0)]
处理顺序: E → C → B → A
```

### 2. 逐列处理
- **E列处理**：读取E列图片 → 在E列后插入F列 → 写入LaTeX到F列
- **C列处理**：读取C列图片 → 在C列后插入D列 → 写入LaTeX到D列  
- **B列处理**：读取B列图片 → 在B列后插入C列 → 写入LaTeX到C列
- **A列处理**：读取A列图片 → 在A列后插入B列 → 写入LaTeX到B列

### 3. 最终结果
```
原始表格: A | B | C | D | E
处理后:   A | B | C | D | E | F | G | H | I | J
         图 |LaTeX|图|LaTeX|图|LaTeX|图|LaTeX|图|LaTeX
```

## 支持的列格式

### 列名格式
- **单字母**：A, B, C, ..., Z
- **多字母**：AA, AB, AC, ..., ZZ, AAA, ...
- **大小写不敏感**：a, b, c 等同于 A, B, C

### 数字索引格式  
- **0-based索引**：0, 1, 2, 3, 4 对应 A, B, C, D, E

### 示例
```json
{
  "columns": ["A", "E"]           // 列名格式
}

{
  "columns": ["0", "4"]           // 索引格式  
}

{
  "columns": ["a", "e"]           // 小写格式
}

{
  "columns": ["A", "AA", "AB"]    // 多字母格式
}
```

## 错误处理

### 常见错误

1. **无效列名**
```json
{
  "columns": ["A1", "XYZ123"]
}
```
响应：`表格批量解析失败: 无效的列参数: A1`

2. **空列数组**
```json
{
  "columns": []
}
```
响应：`解析列不能为空`

3. **表格链接无效**
```json
{
  "tableUrl": "invalid-url"
}
```
响应：`表格批量解析失败: 无法从链接中提取token`

### 部分成功处理
如果某些列处理失败，系统会继续处理其他列：
```
表格批量解析成功，链接: xxx，解析列数: 3，成功转换: 2，失败转换: 1
```

## 性能特点

### 优势
- **避免索引偏移**：从右到左处理，完全解决列偏移问题
- **保持兼容性**：现有单列处理功能不受影响
- **错误隔离**：单列失败不影响其他列处理

### 注意事项
- **处理时间**：多列处理时间 = 单列处理时间 × 列数
- **API调用**：每列都会调用飞书API进行插入和写入操作
- **内存使用**：会同时加载所有列的图片数据

## 与现有功能对比

| 特性 | 原有逐个处理 | 新批量处理 |
|------|-------------|-----------|
| 列索引偏移 | ❌ 存在问题 | ✅ 已解决 |
| 处理顺序 | 从左到右 | 从右到左 |
| API兼容性 | ✅ 完全兼容 | ✅ 完全兼容 |
| 错误处理 | 单列失败全部停止 | 单列失败继续处理 |
| 返回信息 | 简单成功信息 | 详细统计信息 |

## 测试建议

### 基础测试
```bash
# 测试相邻列
{"columns": ["A", "B"]}

# 测试间隔列  
{"columns": ["A", "E"]}

# 测试多列
{"columns": ["A", "C", "E", "G"]}
```

### 边界测试
```bash
# 测试单列
{"columns": ["A"]}

# 测试大量列
{"columns": ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"]}
```

### 异常测试
```bash
# 测试无效列名
{"columns": ["A", "XYZ", "E"]}

# 测试空表格
{"tableUrl": "empty-table-url", "columns": ["A"]}
```
