# 批量列处理设计文档

## 问题描述

当用户设置 `columns=["A","E"]` 时，希望位于A列和E列的图片转成LaTeX。但原有实现存在问题：

1. **列索引动态变化**：处理A列时会在A列后插入一列，导致原来的E列变成F列
2. **逐个处理导致偏移**：每次调用 `test()` 方法都会插入列，影响后续列的索引
3. **用户预期不符**：用户指定的E列实际上没有被处理

## 解决方案

采用 **插入列模式（倒序执行）** 方案：

### 核心思路
- 从右到左处理列，避免列索引偏移问题
- 先处理E列，再处理A列，这样A列插入不会影响E列的位置

### 实现逻辑

#### 1. 新增批量处理方法
```java
public ImageToLatexProcessResult processBatchColumnsWithInsert(String wikiUrl, String[] columns)
```

#### 2. 处理流程
1. **解析并排序列**：将列名转换为列索引，按索引从大到小排序
2. **从右到左处理**：E列(4) → A列(0)，避免索引偏移
3. **逐列插入处理**：每列处理完成后插入一列
4. **统计结果**：汇总所有列的处理结果

#### 3. 处理顺序示例
```
输入: ["A", "E"]
解析: A=0, E=4  
排序: [E(4), A(0)]
处理: E列 → A列
```

### 优势分析

1. **解决核心问题**：完全避免列索引偏移问题
2. **保持现有行为**：仍然插入列，不会覆盖相邻列内容
3. **向后兼容**：保持现有单列处理API不变
4. **用户体验好**：A列图片→B列LaTeX，E列图片→F列LaTeX

### API使用

```bash
POST /latex/parse-table-json
{
  "tableUrl": "https://xxx.feishu.cn/wiki/token",
  "columns": ["A", "E"]
}
```

**处理结果**：
- E列图片 → F列LaTeX（先处理，不受影响）
- A列图片 → B列LaTeX（后处理，E列已完成）

### 代码变更

#### 1. LatexService.java
- 新增 `processBatchColumnsWithInsert()` 方法
- 新增 `ColumnInfo` 内部类
- 添加列排序和批量处理逻辑

#### 2. LatexController.java  
- 修改 `parseTableJson()` 方法
- 使用新的批量处理方法
- 改进返回结果格式

#### 3. 保持兼容性
- 现有的 `test()` 方法保持不变
- 单列处理功能完全兼容
- 按坐标处理功能不受影响

## 测试场景

### 场景1：基本功能测试
```json
{
  "tableUrl": "https://xxx.feishu.cn/wiki/token",
  "columns": ["A", "E"]
}
```
**预期**：E列先处理，A列后处理，都能正确转换

### 场景2：相邻列测试
```json
{
  "tableUrl": "https://xxx.feishu.cn/wiki/token", 
  "columns": ["A", "B"]
}
```
**预期**：B列先处理，A列后处理，避免覆盖问题

### 场景3：多列测试
```json
{
  "tableUrl": "https://xxx.feishu.cn/wiki/token",
  "columns": ["A", "C", "E", "G"]
}
```
**预期**：处理顺序 G→E→C→A，所有列都能正确处理

## 实现状态

- ✅ 新增批量处理方法
- ✅ 实现列排序逻辑  
- ✅ 修改控制器接口
- ✅ 添加内部类支持
- ✅ 保持向后兼容性

## 后续优化

1. **性能优化**：考虑并行处理非相邻列
2. **错误处理**：改进单列失败时的处理逻辑
3. **日志优化**：添加更详细的处理进度日志
4. **测试覆盖**：添加单元测试和集成测试
