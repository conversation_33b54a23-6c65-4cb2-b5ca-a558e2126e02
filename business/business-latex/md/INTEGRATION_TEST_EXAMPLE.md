# 按坐标处理功能集成测试示例

## 测试准备

### 1. 准备测试数据
在飞书表格中准备以下测试数据：
- A1: 包含一张数学公式图片
- A2: 包含另一张数学公式图片  
- E1: 包含第三张数学公式图片
- B1, B2, F1: 确保这些位置为空（用于写入LaTeX结果）

### 2. 获取Wiki链接
获取包含上述表格的飞书Wiki页面链接，格式如：
```
https://xxx.feishu.cn/wiki/OXaCwMKF1ie5YHkVOhPcYlvlnQc
```

## 测试用例

### 测试用例1：单个坐标处理
**目的**：验证单个坐标的图片转LaTeX功能

**请求**：
```bash
curl -X POST http://localhost:8080/latex/parse-table-by-cells \
  -H "Content-Type: application/json" \
  -d '{
    "tableUrl": "https://xxx.feishu.cn/wiki/OXaCwMKF1ie5YHkVOhPcYlvlnQc",
    "cellAddresses": ["A1"]
  }'
```

**预期结果**：
- A1的图片被转换为LaTeX表达式
- LaTeX结果写入B1单元格
- 返回成功消息：`表格按坐标解析成功，链接: xxx，处理坐标数: 1，成功转换: 1，失败转换: 0`

### 测试用例2：多个坐标批量处理
**目的**：验证批量处理多个坐标的功能

**请求**：
```bash
curl -X POST http://localhost:8080/latex/parse-table-by-cells \
  -H "Content-Type: application/json" \
  -d '{
    "tableUrl": "https://xxx.feishu.cn/wiki/OXaCwMKF1ie5YHkVOhPcYlvlnQc",
    "cellAddresses": ["A1", "A2", "E1"]
  }'
```

**预期结果**：
- A1的图片 → B1写入LaTeX
- A2的图片 → B2写入LaTeX
- E1的图片 → F1写入LaTeX
- 返回成功消息：`表格按坐标解析成功，链接: xxx，处理坐标数: 3，成功转换: 3，失败转换: 0`

### 测试用例3：包含无效坐标
**目的**：验证错误处理机制

**请求**：
```bash
curl -X POST http://localhost:8080/latex/parse-table-by-cells \
  -H "Content-Type: application/json" \
  -d '{
    "tableUrl": "https://xxx.feishu.cn/wiki/OXaCwMKF1ie5YHkVOhPcYlvlnQc",
    "cellAddresses": ["A1", "Z999", "E1"]
  }'
```

**预期结果**：
- A1和E1的图片正常处理
- Z999坐标处理失败（如果超出表格范围）
- 返回部分成功消息：`表格按坐标解析成功，链接: xxx，处理坐标数: 3，成功转换: 2，失败转换: 1`

### 测试用例4：空单元格处理
**目的**：验证空单元格的处理

**请求**：
```bash
curl -X POST http://localhost:8080/latex/parse-table-by-cells \
  -H "Content-Type: application/json" \
  -d '{
    "tableUrl": "https://xxx.feishu.cn/wiki/OXaCwMKF1ie5YHkVOhPcYlvlnQc",
    "cellAddresses": ["A1", "C1", "E1"]
  }'
```

**预期结果**（假设C1为空）：
- A1和E1的图片正常处理
- C1因为不包含图片而被跳过
- 返回部分成功消息

## 验证步骤

### 1. 功能验证
- [ ] 检查指定的目标单元格是否写入了LaTeX表达式
- [ ] 验证LaTeX表达式的正确性
- [ ] 确认没有插入新的列
- [ ] 检查日志输出是否正常

### 2. 性能验证
- [ ] 记录处理时间
- [ ] 验证内存使用情况
- [ ] 检查并发处理能力

### 3. 兼容性验证
- [ ] 确认现有的按列处理功能仍然正常
- [ ] 验证两种功能可以同时使用
- [ ] 检查是否影响其他模块

## 日志检查

### 关键日志信息
```
INFO  - 开始按坐标处理图片转LaTeX: https://xxx.feishu.cn/wiki/xxx, 坐标数量: 3
INFO  - 处理坐标列表: A1, A2, E1
INFO  - 第一步：提取Wiki信息
INFO  - 提取到Wiki Token: OXaCwMKF1ie5YHkVOhPcYlvlnQc
INFO  - 获取到Spreadsheet Token: xxx
INFO  - 第二步：解析坐标并读取单元格数据
INFO  - ✓ 发现图片: A1 -> token: boxXXXXXXXXXXXXXXX
INFO  - ✓ 发现图片: A2 -> token: boxYYYYYYYYYYYYYYY
INFO  - ✓ 发现图片: E1 -> token: boxZZZZZZZZZZZZZZZ
INFO  - 第三步：逐个处理图片转LaTeX
INFO  - 处理第1/3张图片: A1
INFO  - ✓ 图片处理成功: A1 -> B1
INFO  - 处理第2/3张图片: A2
INFO  - ✓ 图片处理成功: A2 -> B2
INFO  - 处理第3/3张图片: E1
INFO  - ✓ 图片处理成功: E1 -> F1
INFO  - 按坐标处理图片转LaTeX完成: 成功处理3个图片
```

## 故障排除

### 常见问题
1. **坐标解析失败**
   - 检查坐标格式是否正确
   - 确认坐标在表格范围内

2. **图片下载失败**
   - 检查网络连接
   - 验证飞书API权限

3. **LaTeX转换失败**
   - 检查图片格式是否支持
   - 验证转换服务是否正常

4. **写入失败**
   - 检查目标单元格是否可写
   - 验证表格权限

### 调试建议
1. 开启DEBUG日志级别查看详细信息
2. 使用单个坐标进行测试
3. 检查飞书API的响应状态
4. 验证表格结构和权限

## 性能基准

### 预期性能指标
- 单个图片处理时间：< 5秒
- 批量处理（3个图片）：< 15秒
- 内存使用：< 100MB
- 成功率：> 95%

### 性能优化建议
1. 考虑并行处理多个图片
2. 实现图片缓存机制
3. 优化网络请求重试策略
4. 添加处理进度反馈
