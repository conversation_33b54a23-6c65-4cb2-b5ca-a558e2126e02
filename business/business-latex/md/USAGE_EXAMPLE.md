# 飞书表格数据读取功能使用示例

## 功能概述

新增的 `readSheetRangeData` 方法可以读取飞书表格中指定范围的数据，作为处理链条中的一个步骤使用。

## 方法签名

```java
public Object[][] readSheetRangeData(String spreadsheetToken, String range)
```

### 参数说明
- `spreadsheetToken`: 表格的token标识
- `range`: 要读取的范围，支持多种格式：
  - `"A:A"` - 读取整个A列
  - `"B:B"` - 读取整个B列  
  - `"A1:A100"` - 读取A1到A100的数据
  - `"A1:C10"` - 读取A1到C10的矩形区域
  - `"Sheet1!A:A"` - 读取指定工作表的A列

### 返回值
- `Object[][]`: 二维数组，表示读取到的单元格数据
- 如果没有数据，返回空的二维数组 `new Object[0][0]`

## 在处理链条中的使用示例

### 示例1：从Wiki链接读取指定列数据

```java
@Service
public class DataProcessingService {
    
    @Autowired
    private LatexService latexService;
    
    public void processWikiTableData(String wikiUrl) {
        try {
            // 第一步：从Wiki链接提取token
            String wikiToken = latexService.extractTokenFromWikiUrl(wikiUrl);
            
            // 第二步：获取spreadsheet_token
            String spreadsheetToken = latexService.getSpreadsheetTokenFromWiki(wikiToken);
            
            // 第三步：获取sheet_id列表
            List<String> sheetIds = latexService.getSheetIds(spreadsheetToken);
            
            // 第四步：读取第一个工作表的A列数据
            Object[][] columnData = latexService.readSheetRangeData(spreadsheetToken, "A:A");
            
            // 第五步：处理数据（您的后续业务逻辑）
            processColumnData(columnData);
            
        } catch (Exception e) {
            log.error("处理Wiki表格数据失败", e);
        }
    }
    
    private void processColumnData(Object[][] data) {
        if (data == null || data.length == 0) {
            log.info("没有读取到数据");
            return;
        }
        
        log.info("读取到 {} 行数据", data.length);
        
        // 处理每一行数据
        for (int i = 0; i < data.length; i++) {
            Object[] row = data[i];
            if (row.length > 0) {
                Object cellValue = row[0]; // 获取第一列的值
                log.info("第{}行数据: {}", i + 1, cellValue);
                
                // 在这里添加您的业务处理逻辑
                // 例如：数据验证、格式转换、存储到数据库等
            }
        }
    }
}
```

### 示例2：读取多列数据进行对比分析

```java
public void compareTableColumns(String spreadsheetToken) {
    try {
        // 读取A列数据
        Object[][] columnA = latexService.readSheetRangeData(spreadsheetToken, "A:A");
        
        // 读取B列数据
        Object[][] columnB = latexService.readSheetRangeData(spreadsheetToken, "B:B");
        
        // 对比分析两列数据
        compareColumns(columnA, columnB);
        
    } catch (Exception e) {
        log.error("对比表格列数据失败", e);
    }
}

private void compareColumns(Object[][] columnA, Object[][] columnB) {
    int maxRows = Math.max(columnA.length, columnB.length);
    
    for (int i = 0; i < maxRows; i++) {
        Object valueA = (i < columnA.length && columnA[i].length > 0) ? columnA[i][0] : null;
        Object valueB = (i < columnB.length && columnB[i].length > 0) ? columnB[i][0] : null;
        
        // 进行对比分析
        if (!Objects.equals(valueA, valueB)) {
            log.info("第{}行数据不一致: A列={}, B列={}", i + 1, valueA, valueB);
        }
    }
}
```

### 示例3：读取指定范围进行数据统计

```java
public void analyzeTableRange(String spreadsheetToken, String sheetId) {
    try {
        // 读取指定工作表的A1到C100范围数据
        String range = sheetId + "!A1:C100";
        Object[][] rangeData = latexService.readSheetRangeData(spreadsheetToken, range);
        
        // 统计分析数据
        analyzeData(rangeData);
        
    } catch (Exception e) {
        log.error("分析表格范围数据失败", e);
    }
}

private void analyzeData(Object[][] data) {
    if (data == null || data.length == 0) {
        log.info("没有数据可分析");
        return;
    }
    
    int totalRows = data.length;
    int totalColumns = data[0].length;
    int nonEmptyCount = 0;
    
    // 统计非空单元格数量
    for (Object[] row : data) {
        for (Object cell : row) {
            if (cell != null && !cell.toString().trim().isEmpty()) {
                nonEmptyCount++;
            }
        }
    }
    
    log.info("数据统计: 总行数={}, 总列数={}, 非空单元格数={}", 
             totalRows, totalColumns, nonEmptyCount);
}
```

## 错误处理

方法会抛出 `RuntimeException`，包含详细的错误信息：

```java
try {
    Object[][] data = latexService.readSheetRangeData(spreadsheetToken, range);
    // 处理数据...
} catch (RuntimeException e) {
    log.error("读取表格数据失败: {}", e.getMessage());
    // 处理错误情况
}
```

## 注意事项

1. **认证自动处理**: 方法使用飞书SDK自动处理 `tenant_access_token` 的获取和刷新
2. **数据类型**: 返回的 `Object[][]` 中的单元格值可能是字符串、数字或其他类型，需要根据实际情况进行类型转换
3. **空数据处理**: 如果指定范围没有数据，会返回空的二维数组
4. **范围格式**: 确保 `range` 参数格式正确，遵循Excel的A1表示法
5. **性能考虑**: 读取大范围数据时可能耗时较长，建议根据实际需要指定合适的范围

## 日志输出

方法执行时会输出详细的日志信息：

```
INFO  - 开始读取表格数据, spreadsheetToken: xxx, range: A:A
INFO  - 成功读取表格数据，实际范围: A1:A50, 行数: 50
```

这些日志有助于调试和监控数据读取过程。
