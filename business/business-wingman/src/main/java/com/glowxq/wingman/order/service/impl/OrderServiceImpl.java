package com.glowxq.wingman.order.service.impl;

import com.glowxq.core.common.entity.BaseUserInfo;
import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.common.exception.common.BusinessException;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.PageUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.security.core.util.LoginUtils;
import com.glowxq.system.applet.pojo.po.AppletUser;
import com.glowxq.system.applet.service.AppletUserService;
import com.glowxq.wechat.pay.pojo.WechatPaymentData;
import com.glowxq.wechat.pay.service.WechatPaymentService;
import com.glowxq.wingman.order.enums.OrderStatus;
import com.glowxq.wingman.order.mapper.OrderMapper;
import com.glowxq.wingman.order.pojo.dto.*;
import com.glowxq.wingman.order.pojo.po.Order;
import com.glowxq.wingman.order.pojo.vo.OrderVO;
import com.glowxq.wingman.order.service.OrderService;
import com.glowxq.wingman.product.pojo.po.Product;
import com.glowxq.wingman.product.service.ProductService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.OutputStream;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Service
@RequiredArgsConstructor
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {

    private final AppletUserService appletUserService;

    private final ProductService productService;

    private final WechatPaymentService wechatPaymentService;

    private static QueryWrapper buildQueryWrapper(OrderListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(Order.class);
        wrapper.eq(Order::getUserId, dto.getUserId(), Utils.isNotNull(dto.getUserId()));
        wrapper.eq(Order::getProductId, dto.getProductId(), Utils.isNotNull(dto.getProductId()));
        wrapper.like(Order::getProductName, dto.getProductName(), Utils.isNotNull(dto.getProductName()));
        wrapper.eq(Order::getProductType, dto.getProductType(), Utils.isNotNull(dto.getProductType()));
        wrapper.eq(Order::getTransactionId, dto.getTransactionId(), Utils.isNotNull(dto.getTransactionId()));
        wrapper.like(Order::getOrderNumber, dto.getOrderNumber(), Utils.isNotNull(dto.getOrderNumber()));
        wrapper.eq(Order::getOrderUser, dto.getOrderUser(), Utils.isNotNull(dto.getOrderUser()));
        wrapper.eq(Order::getOrderPhone, dto.getOrderPhone(), Utils.isNotNull(dto.getOrderPhone()));
        wrapper.like(Order::getName, dto.getName(), Utils.isNotNull(dto.getName()));
        wrapper.eq(Order::getPrice, dto.getPrice(), Utils.isNotNull(dto.getPrice()));
        wrapper.eq(Order::getPaymentAmount, dto.getPaymentAmount(), Utils.isNotNull(dto.getPaymentAmount()));
        wrapper.eq(Order::getContent, dto.getContent(), Utils.isNotNull(dto.getContent()));
        wrapper.eq(Order::getType, dto.getType(), Utils.isNotNull(dto.getType()));
        wrapper.eq(Order::getInventory, dto.getInventory(), Utils.isNotNull(dto.getInventory()));
        wrapper.eq(Order::getImage, dto.getImage(), Utils.isNotNull(dto.getImage()));
        wrapper.eq(Order::getImageGallery, dto.getImageGallery(), Utils.isNotNull(dto.getImageGallery()));
        wrapper.eq(Order::getExpertUserId, dto.getExpertUserId(), Utils.isNotNull(dto.getExpertUserId()));
        wrapper.like(Order::getExpertName, dto.getExpertName(), Utils.isNotNull(dto.getExpertName()));
        wrapper.eq(Order::getExpertContact, dto.getExpertContact(), Utils.isNotNull(dto.getExpertContact()));
        wrapper.eq(Order::getExpertQrCode, dto.getExpertQrCode(), Utils.isNotNull(dto.getExpertQrCode()));
        wrapper.eq(Order::getServiceTime, dto.getServiceTime(), Utils.isNotNull(dto.getServiceTime()));
        wrapper.eq(Order::getStatus, dto.getStatus(), Utils.isNotNull(dto.getStatus()));
        wrapper.eq(Order::getEnable, dto.getEnable(), Utils.isNotNull(dto.getEnable()));
        wrapper.eq(Order::getBindCode, dto.getBindCode(), Utils.isNotNull(dto.getBindCode()));
        wrapper.orderBy(Order::getId).desc();
        return wrapper;
    }

    @Override
    public void create(OrderCreateDTO dto) {
        Order order = BeanCopyUtils.copy(dto, Order.class);
        save(order);
    }

    @Override
    public void update(OrderUpdateDTO dto) {
        Order order = BeanCopyUtils.copy(dto, Order.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
                              .eq(Order::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(order);
    }

    @Override
    public PageResult<OrderVO> page(OrderListDTO dto) {
        Page<OrderVO> page = pageAs(PageUtils.getPage(dto), buildQueryWrapper(dto), OrderVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<OrderVO> list(OrderListDTO dto) {
        return listAs(buildQueryWrapper(dto), OrderVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public OrderVO detail(Object id) {
        Order order = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(order);
        return BeanCopyUtils.copy(order, OrderVO.class);
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<OrderImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), OrderImportDTO.class, true);
        List<OrderImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(OrderListDTO dto, HttpServletResponse response) {
        List<OrderVO> list = list(dto);
        String fileName = "订单模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "订单", OrderVO.class, os);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Order create(ClientOrderCreateDTO dto) {
        Product product = productService.getById(dto.getProductId());
        // 校验产品是否存在 且启用
        if (product == null || !product.getEnable()) {
            throw new RuntimeException("产品不存在或未启用");
        }
        // 产品库存校验
        CommonResponseEnum.BUSINESS_ERROR.assertTrue(product.getInventory() < dto.getQuantity());
        product.setInventory(product.getInventory() - dto.getQuantity());
        productService.updateById(product);
        Order order = dto.buildEntity();
        BaseUserInfo userInfo = LoginUtils.getLoginUser().getUserInfo();
        AppletUser bindUser = appletUserService.getByBindCode(userInfo.getBindCode());
        ClientOrderCreateDTO.buildOrder(dto, order, product, userInfo, bindUser);
        save(order);
        return order;
    }

    @Override
    public void update(ClientOrderUpdateDTO dto) {
        Order order = mapper.selectOneById(dto.getId());
        CommonResponseEnum.INVALID_ID.assertNull(order);
        order.setStatus(dto.getStatus().name());
        mapper.update(order);
    }

    @Override
    public Order getByOrderNumber(String orderNumber) {
        return mapper.getByOrderNumber(orderNumber);
    }

    @Override
    public WechatPaymentData payment(Long orderId) {
        Order order = mapper.selectOneById(orderId);
        CommonResponseEnum.INVALID_ID.assertNull(order);
        if (!OrderStatus.Init.equals(order.status())) {
            throw new BusinessException("不允许支付：" + order.status().getName());
        }
        if (order.getCreateTime().plusMinutes(10).isBefore(LocalDateTime.now())) {
            order.setStatus(OrderStatus.Cancel.getCode());
            mapper.update(order);
            throw new BusinessException("订单已过期");
        }

        return wechatPaymentService.payment(order.getOrderNumber(), order.getPrice(), order.getOpenid(), order.getProductName());
    }

    @Override
    public List<Order> listByStatus(OrderStatus orderStatus) {
        return mapper.listByStatus(orderStatus);
    }
}