package com.glowxq.wingman.article.pojo.po;

import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.mysql.EntityChangeListener;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 案例
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
@Table(value = "article", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "案例")
public class Article implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description = "商品ID")
    private Long id;

    @Schema(description = "案例用户")
    private Long userId;

    @Schema(description = "案例专家")
    private Long expertUserId;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "案例人")
    private String articleUser;

    @Schema(description = "案例标题")
    private String title;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "富文本介绍")
    private String content;

    @ExcelProperty(value = "成果摘要")
    @Schema(description = "成果摘要")
    private String achievement;

    @Schema(description = "案例主图")
    private String image;

    @Schema(description = "案例图集")
    private String imageGallery;

    @Schema(description = "专家联系方式")
    private String expertContact;

    @Schema(description = "专家二维码")
    private String expertQrCode;

    @Schema(description = "状态")
    private Boolean enable;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "是否删除")
    private String delFlag;

    @Schema(description = "创建人ID")
    private Long createId;

    @Schema(description = "更新人ID")
    private Long updateId;
}