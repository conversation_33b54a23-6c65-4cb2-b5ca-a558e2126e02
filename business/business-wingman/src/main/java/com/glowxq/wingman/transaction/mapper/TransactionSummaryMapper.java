package com.glowxq.wingman.transaction.mapper;

import com.glowxq.wingman.transaction.pojo.po.TransactionSummary;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;

/**
 * <p>
 * 流水报告 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
public interface TransactionSummaryMapper extends BaseMapper<TransactionSummary> {

    default TransactionSummary getByOrderNumber(String orderNumber) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(TransactionSummary::getOrderNumber, orderNumber);
        return this.selectOneByQuery(queryWrapper);
    }
}