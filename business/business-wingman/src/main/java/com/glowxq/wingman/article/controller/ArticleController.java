package com.glowxq.wingman.article.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.wingman.article.pojo.dto.ArticleCreateDTO;
import com.glowxq.wingman.article.pojo.dto.ArticleListDTO;
import com.glowxq.wingman.article.pojo.dto.ArticleUpdateDTO;
import com.glowxq.wingman.article.pojo.vo.ArticleVO;
import com.glowxq.wingman.article.service.ArticleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * article/案例 Controller
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Tag(name = "案例")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class ArticleController extends BaseApi {

    private final ArticleService articleService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "article.create")
    @PostMapping("/article/create")
    public ApiResult<Void> create(@RequestBody ArticleCreateDTO dto) {
        articleService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "article.update")
    @PutMapping("/article/update")
    public ApiResult<Void> update(@RequestBody ArticleUpdateDTO dto) {
        articleService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "article.remove")
    @DeleteMapping("/article/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        articleService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "article.query_table")
    @PostMapping("/article/list")
    public ApiResult<PageResult<ArticleVO>> list(@RequestBody ArticleListDTO dto) {
        return ApiPageResult.success(articleService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "article.query_table")
    @GetMapping("/article/detail")
    public ApiResult<ArticleVO> detail(@RequestParam Object id) {
        return ApiResult.success(articleService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "article.import")
    @PostMapping("/article/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        articleService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "article.export")
    @PostMapping("/article/export")
    public void exportExcel(@RequestBody ArticleListDTO dto, HttpServletResponse response) {
        articleService.exportExcel(dto, response);
    }
}