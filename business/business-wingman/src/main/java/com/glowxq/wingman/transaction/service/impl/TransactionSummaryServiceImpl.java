package com.glowxq.wingman.transaction.service.impl;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.PageUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.wingman.transaction.mapper.TransactionSummaryMapper;
import com.glowxq.wingman.transaction.pojo.dto.TransactionSummaryCreateDTO;
import com.glowxq.wingman.transaction.pojo.dto.TransactionSummaryImportDTO;
import com.glowxq.wingman.transaction.pojo.dto.TransactionSummaryListDTO;
import com.glowxq.wingman.transaction.pojo.dto.TransactionSummaryUpdateDTO;
import com.glowxq.wingman.transaction.pojo.po.TransactionSummary;
import com.glowxq.wingman.transaction.pojo.vo.TransactionSummaryVO;
import com.glowxq.wingman.transaction.service.TransactionSummaryService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.util.List;

/**
 * <p>
 * 流水报告 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Service
@RequiredArgsConstructor
public class TransactionSummaryServiceImpl extends ServiceImpl<TransactionSummaryMapper, TransactionSummary> implements TransactionSummaryService {

    private static QueryWrapper buildQueryWrapper(TransactionSummaryListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(TransactionSummary.class);
        wrapper.eq(TransactionSummary::getOrderId, dto.getOrderId(), Utils.isNotNull(dto.getOrderId()));
        wrapper.eq(TransactionSummary::getOrderNumber, dto.getOrderNumber(), Utils.isNotNull(dto.getOrderNumber()));
        wrapper.eq(TransactionSummary::getTransactionNumber, dto.getTransactionNumber(), Utils.isNotNull(dto.getTransactionNumber()));
        wrapper.eq(TransactionSummary::getOrderAmount, dto.getOrderAmount(), Utils.isNotNull(dto.getOrderAmount()));
        wrapper.eq(TransactionSummary::getPayoutAmount, dto.getPayoutAmount(), Utils.isNotNull(dto.getPayoutAmount()));
        wrapper.eq(TransactionSummary::getReceiveAmount, dto.getReceiveAmount(), Utils.isNotNull(dto.getReceiveAmount()));
        wrapper.eq(TransactionSummary::getAmount, dto.getAmount(), Utils.isNotNull(dto.getAmount()));
        wrapper.eq(TransactionSummary::getEnable, dto.getEnable(), Utils.isNotNull(dto.getEnable()));
        return wrapper;
    }

    @Override
    public void create(TransactionSummaryCreateDTO dto) {
        TransactionSummary transactionSummary = BeanCopyUtils.copy(dto, TransactionSummary.class);
        save(transactionSummary);
    }

    @Override
    public void update(TransactionSummaryUpdateDTO dto) {
        TransactionSummary transactionSummary = BeanCopyUtils.copy(dto, TransactionSummary.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
                              .eq(TransactionSummary::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(transactionSummary);
    }

    @Override
    public PageResult<TransactionSummaryVO> page(TransactionSummaryListDTO dto) {
        Page<TransactionSummaryVO> page = pageAs(PageUtils.getPage(dto), buildQueryWrapper(dto), TransactionSummaryVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<TransactionSummaryVO> list(TransactionSummaryListDTO dto) {
        return listAs(buildQueryWrapper(dto), TransactionSummaryVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public TransactionSummaryVO detail(Long id) {
        TransactionSummary transactionSummary = getById(id);
        CommonResponseEnum.INVALID_ID.assertNull(transactionSummary);
        return BeanCopyUtils.copy(transactionSummary, TransactionSummaryVO.class);
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<TransactionSummaryImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), TransactionSummaryImportDTO.class, true);
        List<TransactionSummaryImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(TransactionSummaryListDTO dto, HttpServletResponse response) {
        List<TransactionSummaryVO> list = list(dto);
        String fileName = "流水报告模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "流水报告", TransactionSummaryVO.class, os);
    }

    @Override
    public TransactionSummary getByOrderNumber(String orderNumber) {
        return mapper.getByOrderNumber(orderNumber);
    }
}