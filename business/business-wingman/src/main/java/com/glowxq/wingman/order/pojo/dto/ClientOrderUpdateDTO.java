package com.glowxq.wingman.order.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.wingman.order.enums.OrderStatus;
import com.glowxq.wingman.order.pojo.po.Order;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * Order修改DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
@Schema(description = "Order修改DTO")
public class ClientOrderUpdateDTO implements BaseDTO {

    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "订单状态")
    private OrderStatus status;

    @Override
    public Order buildEntity() {
        return BeanCopyUtils.copy(this, Order.class);
    }
}