package com.glowxq.wingman.transaction.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.wingman.transaction.pojo.dto.TransactionSummaryCreateDTO;
import com.glowxq.wingman.transaction.pojo.dto.TransactionSummaryListDTO;
import com.glowxq.wingman.transaction.pojo.dto.TransactionSummaryUpdateDTO;
import com.glowxq.wingman.transaction.pojo.vo.TransactionSummaryVO;
import com.glowxq.wingman.transaction.service.TransactionSummaryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * transaction/流水报告 Controller
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Tag(name = "流水报告")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class TransactionSummaryController extends BaseApi {

    private final TransactionSummaryService transactionSummaryService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "transaction.summary.create")
    @PostMapping("/transaction-summary/create")
    public ApiResult<Void> create(@RequestBody TransactionSummaryCreateDTO dto) {
        transactionSummaryService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "transaction.summary.update")
    @PutMapping("/transaction-summary/update")
    public ApiResult<Void> update(@RequestBody TransactionSummaryUpdateDTO dto) {
        transactionSummaryService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "transaction.summary.remove")
    @DeleteMapping("/transaction-summary/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        transactionSummaryService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "transaction.summary.query_table")
    @GetMapping("/transaction-summary/list")
    public ApiResult<PageResult<TransactionSummaryVO>> list(TransactionSummaryListDTO dto) {
        return ApiPageResult.success(transactionSummaryService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "transaction.summary.query_table")
    @GetMapping("/transaction-summary/detail")
    public ApiResult<TransactionSummaryVO> detail(@RequestParam Long id) {
        return ApiResult.success(transactionSummaryService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "transaction.summary.import")
    @PostMapping("/transaction-summary/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        transactionSummaryService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "transaction.summary.export")
    @PostMapping("/transaction-summary/export")
    public void exportExcel(@RequestBody TransactionSummaryListDTO dto, HttpServletResponse response) {
        transactionSummaryService.exportExcel(dto, response);
    }
}