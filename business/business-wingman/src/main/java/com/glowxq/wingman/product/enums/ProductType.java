package com.glowxq.wingman.product.enums;

import com.glowxq.core.common.enums.base.BaseType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/20
 */
@AllArgsConstructor
@Getter
public enum ProductType implements BaseType {

    Product("Product", "普通商品"),
    Teacher("Teacher", "导师"),
    ;

    /**
     * code
     */
    private final String code;

    /**
     * 最近类型
     */
    private final String name;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static ProductType matchCode(String code) {
        for (ProductType productType : ProductType.values()) {
            if (productType.getCode().equals(code)) {
                return productType;
            }
        }
        return null;
    }
}
