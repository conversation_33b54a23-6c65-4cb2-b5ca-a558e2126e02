package com.glowxq.wingman.order.mapper;

import com.glowxq.wingman.order.enums.OrderStatus;
import com.glowxq.wingman.order.pojo.po.Order;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;

import java.util.List;

/**
 * <p>
 * 订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
public interface OrderMapper extends BaseMapper<Order> {

    default Order getByOrderNumber(String orderNumber) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(Order::getOrderNumber, orderNumber);
        return selectOneByQuery(queryWrapper);
    }

    default List<Order> listByStatus(OrderStatus orderStatus) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(Order::getStatus, orderStatus.getCode());
        return selectListByQuery(queryWrapper);
    }
}