package com.glowxq.wingman.transaction.pojo.dto;

import com.glowxq.core.common.entity.PageQuery;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.wingman.transaction.pojo.po.TransactionSummary;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * TransactionSummary查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Data
@Schema(description = "TransactionSummary查询DTO")
public class TransactionSummaryListDTO extends PageQuery implements BaseDTO {

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单号")
    private String orderNumber;

    @Schema(description = "流水号")
    private String transactionNumber;

    @Schema(description = "订单金额")
    private BigDecimal orderAmount;

    @Schema(description = "支出金额")
    private BigDecimal payoutAmount;

    @Schema(description = "退还金额")
    private BigDecimal receiveAmount;

    @Schema(description = "利润金额")
    private BigDecimal amount;

    @Schema(description = "启用状态")
    private Boolean enable;

    @Override
    public TransactionSummary buildEntity() {
        return BeanCopyUtils.copy(this, TransactionSummary.class);
    }
}