package com.glowxq.wingman.order.enums;

import com.glowxq.core.common.enums.base.BaseType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/20
 */
@AllArgsConstructor
@Getter
public enum OrderStatus implements BaseType {

    Init("Init", "未支付"),
    Payment("Payment", "已支付"),
    Complete("Complete", "完成"),
    Cancel("Cancel", "取消"),
    AfterSales("AfterSales", "售后"),
    AfterComplete("AfterComplete", "售后完成"),
    Close("Close", "关闭"),
    ;

    /**
     * code
     */
    private final String code;

    /**
     * 最近类型
     */
    private final String name;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static OrderStatus matchCode(String code) {
        for (OrderStatus orderStatus : OrderStatus.values()) {
            if (orderStatus.getCode().equals(code)) {
                return orderStatus;
            }
        }
        return null;
    }

    public Boolean isClientHandleStatus() {
        return this.equals(Complete) || this.equals(Cancel) || this.equals(AfterSales);
    }
}
