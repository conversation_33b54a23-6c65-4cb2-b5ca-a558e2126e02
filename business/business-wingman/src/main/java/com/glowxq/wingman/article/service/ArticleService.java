package com.glowxq.wingman.article.service;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.wingman.article.pojo.dto.ArticleCreateDTO;
import com.glowxq.wingman.article.pojo.dto.ArticleListDTO;
import com.glowxq.wingman.article.pojo.dto.ArticleUpdateDTO;
import com.glowxq.wingman.article.pojo.po.Article;
import com.glowxq.wingman.article.pojo.vo.ArticleVO;
import com.mybatisflex.core.service.IService;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 案例 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
public interface ArticleService extends IService<Article> {

    void create(ArticleCreateDTO dto);

    void update(ArticleUpdateDTO dto);

    PageResult<ArticleVO> page(ArticleListDTO dto);

    List<ArticleVO> list(ArticleListDTO dto);

    void remove(SelectIdsDTO dto);

    ArticleVO detail(Object id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(ArticleListDTO dto, HttpServletResponse response);
}