package com.glowxq.wingman.auth;

import cn.dev33.satoken.stp.SaLoginModel;
import com.glowxq.core.common.entity.LoginUser;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.util.JsonUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.platform.strategy.AppletStrategy;
import com.glowxq.security.core.util.LoginUtils;
import com.glowxq.security.pojo.ClientVO;
import com.glowxq.security.pojo.LoginInfo;
import com.glowxq.security.pojo.LoginVO;
import com.glowxq.system.admin.service.SysUserService;
import com.glowxq.system.applet.service.AppletUserService;
import com.glowxq.wechat.applet.WechatService;
import com.glowxq.wechat.applet.pojo.LoginInfoResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class WingmanPasswordStrategy extends AppletStrategy {

    public WingmanPasswordStrategy(WechatService wechatService, AppletUserService appletUserService, SysUserService sysUserService) {
        super(wechatService, appletUserService, sysUserService);
    }

    @Override
    public LoginVO login(LoginInfo info, ClientVO client) {
        String clientId = client.getClientId();
        String password = info.getPassword();
        String username = info.getUsername();
        // 设置登录模型
        SaLoginModel model = createLoginModel(client);
        // 复制用户信息
        LoginUser loginUser = appletUserService.passwordLogin(username, password);
        Long userId = loginUser.getUserInfo().getId();
        // 设置jwt额外数据
        Map<String, Object> extraData = createExtraData(clientId, userId);
        // 执行登录
        LoginUtils.performLogin(loginUser, model, extraData);
        // 构造返回对象
        return createLoginVO(loginUser);
    }

    @Override
    protected LoginVO doLogin(LoginInfo info, ClientVO client) {
        String clientId = client.getClientId();
        String code = info.getCode();
        CommonResponseEnum.INVALID.message("无效的小程序code").assertFalse(Utils.isNotNull(code));

        String accessToken = wechatService.getAccessToken();
        LoginInfoResult result = wechatService.login(code, accessToken);
        log.info(" 小程序登录返回信息：{}", JsonUtils.toJsonString(result));
        String openid = result.getOpenid();
        String unionid = result.getUnionId();
        String sessionKey = result.getSessionKey(); // 小程序登录凭证
        log.info("小程序登录返回信息：openid={}, unionid={}, sessionKey={}", openid, unionid, sessionKey);
        LoginUser loginUser = appletUserService.loginInfo(openid, unionid, info.getAutoRegister());

        // 设置登录模型
        SaLoginModel model = createLoginModel(client);

        // 设置jwt额外数据
        Map<String, Object> extraData = createExtraData(clientId, loginUser.getUserInfo().getId());

        // 执行登录
        LoginUtils.performLogin(loginUser, model, extraData);

        // 构造返回对象
        return createLoginVO(loginUser);
    }
}
