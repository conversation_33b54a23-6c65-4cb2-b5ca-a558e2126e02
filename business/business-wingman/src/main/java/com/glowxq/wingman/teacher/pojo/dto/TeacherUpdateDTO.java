package com.glowxq.wingman.teacher.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.wingman.teacher.pojo.po.Teacher;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Teacher修改DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@Schema(description = "Teacher修改DTO")
public class TeacherUpdateDTO implements BaseDTO {

    @Schema(description = "教师ID")
    private Long id;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "商品名")
    private String name;

    @Schema(description = "商品副标题")
    private String subName;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "简介")
    private String simpleContent;

    @Schema(description = "富文本介绍")
    private String content;

    @Schema(description = "商品类型")
    private String type;

    @Schema(description = "排序字段，数值越大排序越考前")
    private Integer sort;

    @Schema(description = "库存")
    private Integer inventory;

    @Schema(description = "下单数量")
    private Integer quantity;

    @Schema(description = "商品主图")
    private String image;

    @Schema(description = "详情图片")
    private String contentImage;

    @Schema(description = "商品图集")
    private String imageGallery;

    @Schema(description = "专家用户ID")
    private Long expertUserId;

    @Schema(description = "专家姓名")
    private String expertName;

    @Schema(description = "专家联系方式")
    private String expertContact;

    @Schema(description = "专家跳转链接")
    private String expertUrl;

    @Schema(description = "专家二维码")
    private String expertQrCode;

    @Schema(description = "专家服务周期")
    private String serviceTime;

    @Schema(description = "状态")
    private Boolean enable;

    @Schema(description = "")
    private String tenantId;

    @Schema(description = "标签id")
    private List<Long> tagIds;

    @Override
    public Teacher buildEntity() {
        return BeanCopyUtils.copy(this, Teacher.class);
    }
}