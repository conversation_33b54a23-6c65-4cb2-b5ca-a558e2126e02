package com.glowxq.wingman.order.pojo.dto;

import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.wingman.order.pojo.po.Order;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * Order导入DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
@Schema(description = "Order导入DTO")
public class OrderImportDTO implements BaseDTO {

    @ExcelProperty(value = "下单用户")
    @Schema(description = "下单用户")
    private Long userId;

    @ExcelProperty(value = "产品ID")
    @Schema(description = "产品ID")
    private Long productId;

    @ExcelProperty(value = "产品名")
    @Schema(description = "产品名")
    private String productName;

    @ExcelProperty(value = "商品类型")
    @Schema(description = "商品类型")
    private String productType;

    @ExcelProperty(value = "支付平台交易号")
    @Schema(description = "支付平台交易号")
    private String transactionId;

    @ExcelProperty(value = "订单号")
    @Schema(description = "订单号")
    private String orderNumber;

    @ExcelProperty(value = "下单用户姓名")
    @Schema(description = "下单用户姓名")
    private String orderUser;

    @ExcelProperty(value = "下单用户手机号")
    @Schema(description = "下单用户手机号")
    private String orderPhone;

    @ExcelProperty(value = "商品名")
    @Schema(description = "商品名")
    private String name;

    @ExcelProperty(value = "价格")
    @Schema(description = "价格")
    private BigDecimal price;

    @ExcelProperty(value = "支付金额")
    @Schema(description = "支付金额")
    private BigDecimal paymentAmount;

    @ExcelProperty(value = "富文本介绍")
    @Schema(description = "富文本介绍")
    private String content;

    @Schema(description = "下单数量")
    @Min(value = 1, message = "商品数量至少为1")
    private Integer quantity;

    @ExcelProperty(value = "商品类型")
    @Schema(description = "订单类型")
    private String type;

    @ExcelProperty(value = "库存")
    @Schema(description = "排序字段，数值越大排序越考前")
    private Integer sort;

    @Schema(description = "库存")
    private Integer inventory;

    @ExcelProperty(value = "商品主图")
    @Schema(description = "商品主图")
    private String image;

    @ExcelProperty(value = "商品图集")
    @Schema(description = "商品图集")
    private String imageGallery;

    @ExcelProperty(value = "专家用户ID")
    @Schema(description = "专家用户ID")
    private Long expertUserId;

    @ExcelProperty(value = "专家姓名")
    @Schema(description = "专家姓名")
    private String expertName;

    @ExcelProperty(value = "专家联系方式")
    @Schema(description = "专家联系方式")
    private String expertContact;

    @ExcelProperty(value = "专家二维码")
    @Schema(description = "专家二维码")
    private String expertQrCode;

    @ExcelProperty(value = "专家服务周期")
    @Schema(description = "专家服务周期")
    private String serviceTime;

    @ExcelProperty(value = "订单状态")
    @Schema(description = "订单状态")
    private String status;

    @ExcelProperty(value = "启用状态")
    @Schema(description = "启用状态")
    private Boolean enable;


    @Schema(description = "备注")
    private String remark;

    @Override
    public Order buildEntity() {
        return BeanCopyUtils.copy(this, Order.class);
    }
}