package com.glowxq.wingman.product.pojo.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.glowxq.core.common.entity.base.BaseVO;
import com.glowxq.system.meta.pojo.po.MetaTag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Product返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
@Schema(description = "Product返回vo")
public class ProductVO implements BaseVO {

    @ExcelIgnore
    @Schema(description = "商品ID")
    private Long id;

    @ExcelProperty(value = "分类ID")
    @Schema(description = "分类ID")
    private Long categoryId;

    @ExcelProperty(value = "商品名")
    @Schema(description = "商品名")
    private String name;

    @ExcelProperty(value = "商品名")
    @Schema(description = "商品名")
    private String subName;

    @ExcelProperty(value = "价格")
    @Schema(description = "价格")
    private BigDecimal price;

    @ExcelProperty(value = "商品简介")
    @Schema(description = "商品简介")
    private String simpleContent;

    @ExcelProperty(value = "富文本介绍")
    @Schema(description = "富文本介绍")
    private String content;

    @ExcelProperty(value = "富文本介绍")
    @Schema(description = "富文本介绍")
    private String contentImage;

    @ExcelProperty(value = "商品类型")
    @Schema(description = "商品类型")
    private String type;

    @ExcelProperty(value = "库存")
    @Schema(description = "排序字段，数值越大排序越考前")
    private Integer sort;

    @Schema(description = "库存")
    private Integer inventory;

    @ExcelProperty(value = "商品主图")
    @Schema(description = "商品主图")
    private String image;

    @ExcelProperty(value = "商品图集")
    @Schema(description = "商品图集")
    private String imageGallery;

    @ExcelProperty(value = "专家用户ID")
    @Schema(description = "专家用户ID")
    private Long expertUserId;

    @ExcelProperty(value = "专家姓名")
    @Schema(description = "专家姓名")
    private String expertName;

    @ExcelProperty(value = "专家联系方式")
    @Schema(description = "专家联系方式")
    private String expertContact;

    @ExcelProperty(value = "专家跳转链接")
    @Schema(description = "专家跳转链接")
    private String expertUrl;

    @ExcelProperty(value = "专家二维码")
    @Schema(description = "专家二维码")
    private String expertQrCode;

    @ExcelProperty(value = "专家服务周期")
    @Schema(description = "专家服务周期")
    private String serviceTime;

    @ExcelProperty(value = "状态")
    @Schema(description = "状态")
    private Boolean enable;

    @Schema(description = "标签标签")
    private List<MetaTag> tagList;
}