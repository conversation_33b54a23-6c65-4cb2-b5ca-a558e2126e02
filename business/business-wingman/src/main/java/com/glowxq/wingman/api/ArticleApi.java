package com.glowxq.wingman.api;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.ApiPageResult;
import com.glowxq.core.common.entity.ApiResult;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.wingman.article.pojo.dto.ArticleListDTO;
import com.glowxq.wingman.article.pojo.vo.ArticleVO;
import com.glowxq.wingman.article.service.ArticleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * article/案例 Api
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Tag(name = "案例")
@RestController
@SaIgnore
@RequestMapping("/client")
@RequiredArgsConstructor
public class ArticleApi extends BaseApi {

    private final ArticleService articleService;

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "article.query_table")
    @PostMapping("/article/list")
    public ApiResult<PageResult<ArticleVO>> list(@RequestBody ArticleListDTO dto) {
        return ApiPageResult.success(articleService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "article.query_table")
    @GetMapping("/article/detail")
    public ApiResult<ArticleVO> detail(@RequestParam Object id) {
        return ApiResult.success(articleService.detail(id));
    }
}