package com.glowxq.wingman.product.pojo.po;

import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.mysql.EntityChangeListener;
import com.glowxq.wingman.product.enums.ProductType;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
@Table(value = "product", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "商品")
public class Product implements BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    @Schema(description = "商品ID")
    private Long id;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "商品名")
    private String name;

    @Schema(description = "商品副标题")
    private String subName;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "富文本介绍")
    private String content;

    @Schema(description = "介绍图片")
    private String contentImage;

    @Schema(description = "商品简介")
    private String simpleContent;

    @Schema(description = "商品类型")
    private String type;

    @Schema(description = "排序字段，数值越大排序越考前")
    private Integer sort;

    @Schema(description = "库存")
    private Integer inventory;

    @Schema(description = "商品主图")
    private String image;

    @Schema(description = "商品图集")
    private String imageGallery;

    @Schema(description = "专家用户ID")
    private Long expertUserId;

    @Schema(description = "专家姓名")
    private String expertName;

    @Schema(description = "专家联系方式")
    private String expertContact;

    @Schema(description = "专家跳转链接")
    private String expertUrl;

    @Schema(description = "专家二维码")
    private String expertQrCode;

    @Schema(description = "专家服务周期")
    private String serviceTime;

    @Schema(description = "状态")
    private Boolean enable;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "是否删除")
    private String delFlag;

    @Schema(description = "创建人ID")
    private Long createId;

    @Schema(description = "更新人ID")
    private Long updateId;

    public ProductType productType() {
        return ProductType.matchCode(type);
    }
}