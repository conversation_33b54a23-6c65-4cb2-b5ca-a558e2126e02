package com.glowxq.wingman.api;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.ApiPageResult;
import com.glowxq.core.common.entity.ApiResult;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.wingman.product.pojo.dto.ProductListDTO;
import com.glowxq.wingman.product.pojo.vo.ProductVO;
import com.glowxq.wingman.product.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * product/商品 Api
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Tag(name = "商品")
@RestController
@SaIgnore
@RequestMapping("/client")
@RequiredArgsConstructor
public class ProductApi extends BaseApi {

    private final ProductService productService;

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "product.query_table")
    @GetMapping("/product/list")
    public ApiResult<PageResult<ProductVO>> list(ProductListDTO dto) {
        return ApiPageResult.success(productService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "product.query_table")
    @GetMapping("/product/detail")
    public ApiResult<ProductVO> detail(@RequestParam Long id) {
        return ApiResult.success(productService.detail(id));
    }
}