package com.glowxq.wingman.api;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.ApiPageResult;
import com.glowxq.core.common.entity.ApiResult;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.wingman.teacher.pojo.dto.TeacherListDTO;
import com.glowxq.wingman.teacher.pojo.vo.TeacherVO;
import com.glowxq.wingman.teacher.service.TeacherService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * teacher/商品 Api
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Tag(name = "商品")
@RestController
@SaIgnore
@RequestMapping("/client")
@RequiredArgsConstructor
public class TeacherApi extends BaseApi {

    private final TeacherService teacherService;

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "teacher.query_table")
    @GetMapping("/teacher/list")
    public ApiResult<PageResult<TeacherVO>> list(TeacherListDTO dto) {
        return ApiPageResult.success(teacherService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "teacher.query_table")
    @GetMapping("/teacher/detail")
    public ApiResult<TeacherVO> detail(@RequestParam Long id) {
        return ApiResult.success(teacherService.detail(id));
    }
}