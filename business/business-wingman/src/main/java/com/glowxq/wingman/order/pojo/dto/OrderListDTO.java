package com.glowxq.wingman.order.pojo.dto;

import com.glowxq.core.common.entity.PageQuery;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.wingman.order.pojo.po.Order;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * Order查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
@Schema(description = "Order查询DTO")
public class OrderListDTO extends PageQuery implements BaseDTO {

    @Schema(description = "下单用户")
    private Long userId;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "产品名")
    private String productName;

    @Schema(description = "商品类型")
    private String productType;

    @Schema(description = "支付平台交易号")
    private String transactionId;

    @Schema(description = "订单号")
    private String orderNumber;

    @Schema(description = "下单用户姓名")
    private String orderUser;

    @Schema(description = "下单用户手机号")
    private String orderPhone;

    @Schema(description = "商品名")
    private String name;

    @Schema(description = "下单数量")
    @Min(value = 1, message = "商品数量至少为1")
    private Integer quantity;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "支付金额")
    private BigDecimal paymentAmount;

    @Schema(description = "富文本介绍")
    private String content;

    @Schema(description = "订单类型")
    private String type;

    @Schema(description = "排序字段，数值越大排序越考前")
    private Integer sort;

    @Schema(description = "库存")
    private Integer inventory;

    @Schema(description = "商品主图")
    private String image;

    @Schema(description = "商品图集")
    private String imageGallery;

    @Schema(description = "专家用户ID")
    private Long expertUserId;

    @Schema(description = "专家姓名")
    private String expertName;

    @Schema(description = "专家联系方式")
    private String expertContact;

    @Schema(description = "专家二维码")
    private String expertQrCode;

    @Schema(description = "专家服务周期")
    private String serviceTime;

    @Schema(description = "绑定分销码")
    private String bindCode;

    @Schema(description = "订单状态")
    private String status;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "启用状态")
    private Boolean enable;

    @Override
    public Order buildEntity() {
        return BeanCopyUtils.copy(this, Order.class);
    }
}