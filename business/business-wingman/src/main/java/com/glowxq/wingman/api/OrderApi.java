package com.glowxq.wingman.api;

import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.ApiPageResult;
import com.glowxq.core.common.entity.ApiResult;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.security.core.util.LoginUtils;
import com.glowxq.wechat.pay.pojo.WechatPaymentData;
import com.glowxq.wingman.order.enums.OrderStatus;
import com.glowxq.wingman.order.pojo.dto.ClientOrderCreateDTO;
import com.glowxq.wingman.order.pojo.dto.ClientOrderPaymentDTO;
import com.glowxq.wingman.order.pojo.dto.ClientOrderUpdateDTO;
import com.glowxq.wingman.order.pojo.dto.OrderListDTO;
import com.glowxq.wingman.order.pojo.po.Order;
import com.glowxq.wingman.order.pojo.vo.OrderVO;
import com.glowxq.wingman.order.service.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * order/订单 Api
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Tag(name = "订单")
@RestController
@RequestMapping("/client")
@RequiredArgsConstructor
public class OrderApi extends BaseApi {

    private final OrderService orderService;

    @Operation(summary = "新增")
    @PostMapping("/order/create")
    public ApiResult<Order> create(@RequestBody ClientOrderCreateDTO dto) {
        Order order = orderService.create(dto);
        return ApiResult.success(order);
    }

    @Operation(summary = "支付")
    @PostMapping("/order/payment")
    public ApiResult<WechatPaymentData> payment(@RequestBody ClientOrderPaymentDTO clientOrderPaymentDTO) {
        WechatPaymentData wechatPaymentData = orderService.payment(clientOrderPaymentDTO.getOrderId());
        return ApiResult.success(wechatPaymentData);
    }

    @Operation(summary = "修改订单状态")
    @PostMapping("/order/updateStatus")
    public ApiResult<Void> updateStatus(@RequestBody ClientOrderUpdateDTO dto) {
        OrderStatus status = dto.getStatus();
        if (status.isClientHandleStatus()) {
            CommonResponseEnum.BUSINESS_ERROR.message(status.getName() + "不能修改订单状态");
        }
        orderService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @GetMapping("/order/list")
    public ApiResult<PageResult<OrderVO>> list(OrderListDTO dto) {
        dto.setUserId(LoginUtils.getUserId());
        return ApiPageResult.success(orderService.page(dto));
    }

    @Operation(summary = "详情")
    @GetMapping("/order/detail")
    public ApiResult<OrderVO> detail(@RequestParam Object id) {
        return ApiResult.success(orderService.detail(id));
    }
}