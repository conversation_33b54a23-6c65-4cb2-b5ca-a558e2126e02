package com.glowxq.wingman.product.service;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.wingman.product.pojo.dto.ProductCreateDTO;
import com.glowxq.wingman.product.pojo.dto.ProductListDTO;
import com.glowxq.wingman.product.pojo.dto.ProductUpdateDTO;
import com.glowxq.wingman.product.pojo.po.Product;
import com.glowxq.wingman.product.pojo.vo.ProductVO;
import com.mybatisflex.core.service.IService;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 商品 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
public interface ProductService extends IService<Product> {

    void create(ProductCreateDTO dto);

    void update(ProductUpdateDTO dto);

    PageResult<ProductVO> page(ProductListDTO dto);

    List<ProductVO> list(ProductListDTO dto);

    void remove(SelectIdsDTO dto);

    ProductVO detail(Long id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(ProductListDTO dto, HttpServletResponse response);
}