package com.glowxq.wingman.task;

import com.glowxq.wingman.order.enums.OrderStatus;
import com.glowxq.wingman.order.pojo.po.Order;
import com.glowxq.wingman.order.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
@Slf4j
public class OrderTask {

    @Autowired
    OrderService orderService;

    /**
     * 订单超时取消 每隔10分钟运行一次
     */
    @Scheduled(cron = "0 */10 * ? * *") // 每10分钟执行一次
    public void cancelOrder() {
        List<Order> orderList = orderService.listByStatus(OrderStatus.Init);
        List<Order> orders = orderList.stream()
                                      .filter(order -> order.getCreateTime().plusMinutes(10).isBefore(LocalDateTime.now()))
                                      .peek(order -> order.setStatus(OrderStatus.Cancel.getCode())).toList();
        orderService.updateBatch(orders);
    }
}
