package com.glowxq.wingman.order.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.wingman.order.pojo.dto.OrderCreateDTO;
import com.glowxq.wingman.order.pojo.dto.OrderListDTO;
import com.glowxq.wingman.order.pojo.dto.OrderUpdateDTO;
import com.glowxq.wingman.order.pojo.vo.OrderVO;
import com.glowxq.wingman.order.service.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * order/订单 Controller
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Tag(name = "订单")
@RestController
@RequestMapping
@RequiredArgsConstructor
public class OrderController extends BaseApi {

    private final OrderService orderService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "order.create")
    @PostMapping("/order/create")
    public ApiResult<Void> create(@RequestBody OrderCreateDTO dto) {
        orderService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "order.update")
    @PutMapping("/order/update")
    public ApiResult<Void> update(@RequestBody OrderUpdateDTO dto) {
        orderService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "order.remove")
    @DeleteMapping("/order/remove")
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        orderService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "order.query_table")
    @GetMapping("/order/list")
    public ApiResult<PageResult<OrderVO>> list(OrderListDTO dto) {
        return ApiPageResult.success(orderService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "order.query_table")
    @GetMapping("/order/detail")
    public ApiResult<OrderVO> detail(@RequestParam Object id) {
        return ApiResult.success(orderService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "order.import")
    @PostMapping("/order/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        orderService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "order.export")
    @PostMapping("/order/export")
    public void exportExcel(@RequestBody OrderListDTO dto, HttpServletResponse response) {
        orderService.exportExcel(dto, response);
    }
}