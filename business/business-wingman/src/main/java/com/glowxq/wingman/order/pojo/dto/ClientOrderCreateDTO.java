package com.glowxq.wingman.order.pojo.dto;

import com.glowxq.core.common.entity.BaseUserInfo;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.StringUtils;
import com.glowxq.system.applet.pojo.po.AppletUser;
import com.glowxq.wingman.order.enums.OrderStatus;
import com.glowxq.wingman.order.pojo.po.Order;
import com.glowxq.wingman.product.pojo.po.Product;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * Order添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
@Schema(description = "Order添加DTO")
public class ClientOrderCreateDTO implements BaseDTO {

    @Schema(description = "产品ID")
    @NotNull
    private Long productId;

    @Min(value = 1, message = "商品数量至少为1")
    @NotNull
    private Integer quantity;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "备注")
    private String remark;

    public static void buildOrder(ClientOrderCreateDTO dto, Order order, Product product, BaseUserInfo userInfo, AppletUser bindUser) {
        BeanCopyUtils.copy(order, dto);

        order.setUserId(userInfo.getId());
        order.setProductId(product.getId());
        order.setProductName(product.getName());
        order.setProductType(product.getType());
        order.setOpenid(userInfo.getOpenid());
        order.setTransactionId("");
        order.setOrderNumber(StringUtils.genNumber("WIN"));
        order.setOrderUser(userInfo.getName());
        order.setOrderPhone(StringUtils.defaultIfBlank(dto.getPhone(), userInfo.getPhone()));
        order.setName(product.getName());
        order.setPrice(product.getPrice().multiply(new BigDecimal(dto.getQuantity())));
        order.setUnitPrice(product.getPrice());
        order.setQuantity(dto.getQuantity());
        order.setPaymentAmount(BigDecimal.ZERO);
        order.setContent(product.getContent());
        order.setType(product.getType());
        order.setInventory(product.getInventory());
        order.setImage(product.getImage());
        order.setImageGallery(product.getImageGallery());
        order.setExpertUserId(product.getExpertUserId());
        order.setExpertName(product.getExpertName());
        order.setExpertContact(product.getExpertContact());
        order.setExpertQrCode(product.getExpertQrCode());
        order.setServiceTime(product.getServiceTime());
        order.setStatus(OrderStatus.Init.getCode());
        order.setBindCode(bindUser.getCode());
        order.setBindUserId(bindUser.getId());
        order.setBindName(bindUser.getName());
        order.setBindPhone(bindUser.getPhone());
        order.setRemark(StringUtils.defaultIfBlank(dto.getRemark(), ""));
        order.setEnable(true);
    }

    @Override
    public Order buildEntity() {
        return BeanCopyUtils.copy(this, Order.class);
    }
}