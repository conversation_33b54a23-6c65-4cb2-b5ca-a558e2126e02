package com.glowxq.wingman.transaction.pojo.dto;

import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.wingman.transaction.pojo.po.Transaction;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * Transaction添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Data
@Schema(description = "Transaction添加DTO")
public class TransactionCreateDTO implements BaseDTO {

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单号")
    private String orderNumber;

    @Schema(description = "流水号")
    private String transactionNumber;

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "交易时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;

    @Schema(description = "支出/收回款")
    private String type;

    @Schema(description = "操作人")
    private String handleUser;

    @Schema(description = "专家用户ID")
    private Long expertUserId;

    @Schema(description = "专家姓名")
    private String expertName;

    @Schema(description = "专家联系方式")
    private String expertContact;

    @Schema(description = "专家二维码")
    private String expertQrCode;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "启用状态")
    private Boolean enable;

    @Override
    public Transaction buildEntity() {
        return BeanCopyUtils.copy(this, Transaction.class);
    }
}