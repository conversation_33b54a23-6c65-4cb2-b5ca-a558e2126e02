<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.oj.group.mapper.GroupMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.oj.group.pojo.po.Group">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="principal_user_id" property="principalUserId"/>
        <result column="principal_name" property="principalName"/>
        <result column="description" property="description"/>
        <result column="color" property="color"/>
        <result column="text_color" property="textColor"/>
        <result column="enable" property="enable"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, code, principal_user_id, principal_name, description, color, text_color, enable, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
