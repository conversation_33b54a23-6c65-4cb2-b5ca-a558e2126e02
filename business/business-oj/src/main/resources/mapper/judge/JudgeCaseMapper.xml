<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.oj.judge.mapper.JudgeCaseMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.oj.judge.pojo.po.JudgeCase">
        <id column="id" property="id"/>
        <result column="judge_id" property="judgeId"/>
        <result column="judge_key" property="judgeKey"/>
        <result column="user_id" property="userId"/>
        <result column="problem_id" property="problemId"/>
        <result column="problem_key" property="problemKey"/>
        <result column="case_id" property="caseId"/>
        <result column="status" property="status"/>
        <result column="time" property="time"/>
        <result column="memory" property="memory"/>
        <result column="score" property="score"/>
        <result column="group_num" property="groupNum"/>
        <result column="seq" property="seq"/>
        <result column="mode" property="mode"/>
        <result column="input_data" property="inputData"/>
        <result column="output_data" property="outputData"/>
        <result column="user_output" property="userOutput"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, judge_id, judge_key, user_id, problem_id, problem_key, case_id, status, time, memory, score, group_num, seq, mode, input_data, output_data, user_output, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
