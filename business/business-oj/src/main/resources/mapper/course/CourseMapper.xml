<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.oj.operation.mapper.CourseMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.oj.course.pojo.po.Course">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="content" property="content"/>
        <result column="url" property="url"/>
        <result column="teacher_user_id" property="teacherUserId"/>
        <result column="teacher_name" property="teacherName"/>
        <result column="teacher_phone" property="teacherPhone"/>
        <result column="status" property="status"/>
        <result column="enable" property="enable"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, content, url, teacher_user_id, teacher_name, teacher_phone, status, enable, start_time, end_time, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
