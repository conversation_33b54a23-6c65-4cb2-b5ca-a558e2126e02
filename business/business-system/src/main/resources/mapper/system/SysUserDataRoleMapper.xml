<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.system.admin.mapper.SysUserDataRoleMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.system.admin.pojo.po.SysUserDataRole">
        <id column="id" property="id"/>
        <result column="role_id" property="roleId"/>
        <result column="user_id" property="userId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, role_id, user_id
    </sql>

</mapper>
