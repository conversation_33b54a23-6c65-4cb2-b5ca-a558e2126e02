<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.system.admin.mapper.SysDictTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.glowxq.system.admin.pojo.po.SysDictType">
        <id column="id" property="id"/>
        <result column="type_name" property="typeName"/>
        <result column="type_code" property="typeCode"/>
        <result column="is_lock" property="isLock"/>
        <result column="is_show" property="isShow"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_time" property="deleteTime"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
        <result column="delete_id" property="deleteId"/>
        <result column="type" property="type"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type_name, type_code, is_lock, is_show, del_flag, remark, create_time, update_time, delete_time, create_id, update_id, delete_id, type
    </sql>


</mapper>
