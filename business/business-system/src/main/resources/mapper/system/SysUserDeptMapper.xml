<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.system.admin.mapper.SysUserDeptMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.system.admin.pojo.po.SysUserDept">
        <id column="id" property="id"/>
        <result column="dept_id" property="deptId"/>
        <result column="user_id" property="userId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dept_id, user_id
    </sql>

</mapper>
