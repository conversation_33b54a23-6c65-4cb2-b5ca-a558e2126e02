<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.system.meta.mapper.MetaImageMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.system.meta.pojo.po.MetaImage">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="image_key" property="imageKey"/>
        <result column="business_type" property="businessType"/>
        <result column="url" property="url"/>
        <result column="content" property="content"/>
        <result column="skip_url" property="skipUrl"/>
        <result column="sort" property="sort"/>
        <result column="enable" property="enable"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, image_key, business_type, url, content, skip_url, sort, enable, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
