<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.system.meta.mapper.MetaRegionMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.system.meta.pojo.po.MetaRegion">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="ancestors" property="ancestors"/>
        <result column="name" property="name"/>
        <result column="pinyin" property="pinyin"/>
        <result column="pinyin_prefix" property="pinyinPrefix"/>
        <result column="level" property="level"/>
        <result column="enable" property="enable"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_id, ancestors, name, pinyin, pinyin_prefix, level, enable, del_flag, create_id, update_id, create_time, update_time, tenant_id
    </sql>

</mapper>
