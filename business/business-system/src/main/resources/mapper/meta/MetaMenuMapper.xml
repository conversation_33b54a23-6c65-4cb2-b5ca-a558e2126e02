<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.system.meta.mapper.MetaMenuMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.system.meta.pojo.po.MetaMenu">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="active_icon" property="activeIcon"/>
        <result column="inactive_icon" property="inactiveIcon"/>
        <result column="icon_type" property="iconType"/>
        <result column="type" property="type"/>
        <result column="path" property="path"/>
        <result column="enable" property="enable"/>
        <result column="sort" property="sort"/>
        <result column="has_children" property="hasChildren"/>
        <result column="is_lock" property="isLock"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, active_icon, inactive_icon, icon_type, type, path, enable, sort, has_children, is_lock, del_flag, remark, create_id, update_id, create_time, update_time
    </sql>

</mapper>
