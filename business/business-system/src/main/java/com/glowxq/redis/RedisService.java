package com.glowxq.redis;

import com.glowxq.core.common.constant.GlobalConstant;
import com.glowxq.core.common.entity.UserPermissionChangeMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 业务级 redis service
 */
@Component
@RequiredArgsConstructor
public class RedisService {

    private final RedisTemplate<Object, Object> redisTemplate;

    /**
     * 发布Permission 变更消息
     *
     * @param message
     *            消息
     */
    public void sendPermissionChangeMsg(UserPermissionChangeMessage message) {
        redisTemplate.convertAndSend(GlobalConstant.CHANGE_PERMISSIONS_SIGNAL, message);
    }

}
