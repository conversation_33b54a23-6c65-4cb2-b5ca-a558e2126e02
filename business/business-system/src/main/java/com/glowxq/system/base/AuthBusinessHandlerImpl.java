package com.glowxq.system.base;

import com.glowxq.core.common.entity.LoginUser;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/23
 */
@Component
public class AuthBusinessHandlerImpl implements AuthBusinessHandler {

    @Override
    public void buildLoginUser(LoginUser loginUser) {

    }

    @Override
    public void afterCheck(Long userId) {

    }

    @Override
    public void loginAfter(Long userId) {

    }
}
