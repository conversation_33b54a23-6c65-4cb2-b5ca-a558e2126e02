package com.glowxq.system.admin.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.entity.UserPermissionChangeMessage;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.common.enums.DeleteFlag;
import com.glowxq.core.common.event.EventPublisher;
import com.glowxq.core.common.exception.common.BusinessException;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.StringUtils;
import com.glowxq.core.util.TreeUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.generator.service.GeneratorTableService;
import com.glowxq.platform.enums.AdminResponseEnum;
import com.glowxq.platform.event.permission.PermissionChangeEvent;
import com.glowxq.platform.event.permission.PermissionMeta;
import com.glowxq.redis.RedisService;
import com.glowxq.security.core.util.LoginUtils;
import com.glowxq.system.admin.enums.MenuMode;
import com.glowxq.system.admin.mapper.SysMenuMapper;
import com.glowxq.system.admin.mapper.SysUserRoleMapper;
import com.glowxq.system.admin.pojo.dto.sysmenu.GenButtonDTO;
import com.glowxq.system.admin.pojo.dto.sysmenu.MenuPermissionDTO;
import com.glowxq.system.admin.pojo.dto.sysmenu.SysMenuCreateDTO;
import com.glowxq.system.admin.pojo.dto.sysmenu.SysMenuListDTO;
import com.glowxq.system.admin.pojo.po.SysMenu;
import com.glowxq.system.admin.pojo.po.SysUserDataRole;
import com.glowxq.system.admin.pojo.po.table.SysMenuTableDef;
import com.glowxq.system.admin.pojo.vo.sysmenu.MenuPermissionVO;
import com.glowxq.system.admin.pojo.vo.sysmenu.MenuTreeVO;
import com.glowxq.system.admin.pojo.vo.sysmenu.SysMenuVO;
import com.glowxq.system.admin.service.SysMenuService;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.glowxq.system.admin.pojo.po.table.SysDataRoleMenuTableDef.SYS_DATA_ROLE_MENU;
import static com.glowxq.system.admin.pojo.po.table.SysMenuTableDef.SYS_MENU;
import static com.glowxq.system.admin.pojo.po.table.SysRoleMenuTableDef.SYS_ROLE_MENU;
import static com.glowxq.system.admin.pojo.po.table.SysUserDataRoleTableDef.SYS_USER_DATA_ROLE;
import static com.glowxq.system.admin.pojo.po.table.SysUserRoleTableDef.SYS_USER_ROLE;

/**
 * <p>
 * 系统菜单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-01
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

    private final SysUserRoleMapper sysUserRoleMapper;

    private final RedisService redisService;

    private final GeneratorTableService generatorTableService;

    private final EventPublisher eventPublisher;

    /**
     * 创建菜单
     *
     * @param dto dto
     */
    @Transactional
    @Override
    public void create(SysMenuCreateDTO dto) {
        SysMenu menu = BeanCopyUtils.copy(dto, SysMenu.class);
        menu.setId(Utils.generateUUIDs());
        QueryWrapper wrapper;
        if (!("1002003").equals(dto.getMenuTypeCd())) { // 对非按钮进行唯一性校验
            wrapper = QueryWrapper.create().eq(SysMenu::getName, dto.getName()).eq(SysMenu::getDelFlag, "F");
            AdminResponseEnum.MENU_NAME_EXISTS.assertTrue(count(wrapper) > 0);

            wrapper = QueryWrapper.create().eq(SysMenu::getPath, dto.getPath()).eq(SysMenu::getDelFlag, "F");
            CommonResponseEnum.EXISTS.message("menuPath已存在").assertTrue(count(wrapper) > 0);
        }

        int deep;
        if (isRoot(dto.getPid())) {
            deep = 1;
            menu.setPid("0");
        }
        else {
            wrapper = QueryWrapper.create().where(SysMenuTableDef.SYS_MENU.ID.eq(dto.getPid()));
            Integer parentDeep = getOne(wrapper).getDeep();
            deep = parentDeep + 1;
        }
        menu.setDeep(deep);
        menu.setCreateId(Objects.requireNonNull(LoginUtils.getLoginUser()).getUserInfo().getId());
        menu.setHasChildren("F");
        save(menu);
        this.mapper.syncTreeDeep();
        this.mapper.syncTreeHasChildren();
        // 发布Permission 变更通知
        UserPermissionChangeMessage message = new UserPermissionChangeMessage(null, true);
        redisService.sendPermissionChangeMsg(message);
    }

    /**
     * 更新菜单
     *
     * @param dto dto
     */
    @Transactional
    @Override
    public void update(SysMenuCreateDTO dto) {
        QueryWrapper wrapper;
        SysMenu menu = BeanCopyUtils.copy(dto, SysMenu.class);
        // 菜单是否存在
        wrapper = QueryWrapper.create().where(SysMenuTableDef.SYS_MENU.ID.eq(dto.getId()));
        CommonResponseEnum.NOT_EXISTS.message("菜单不存在").assertTrue(count(wrapper) < 1);
        menu.setUpdateId(Objects.requireNonNull(LoginUtils.getLoginUser()).getUserInfo().getId());
        menu.setUpdateTime(LocalDateTime.now());
        updateById(menu);
        this.mapper.syncTreeDeep();
        this.mapper.syncTreeHasChildren();

        // 发布Permission 变更通知
        UserPermissionChangeMessage message = new UserPermissionChangeMessage(null, true);
        redisService.sendPermissionChangeMsg(message);
    }

    /**
     * 删除
     *
     * @param dto dto
     */
    @Transactional
    @Override
    public void remove(SelectIdsDTO dto) {
        if (Utils.isNotNull(dto.getStringIds())) {
            // 递归查询下边的子节点id
            List<String> list = this.mapper.selectMenuAndChildrenIds(dto.getStringIds());
            this.mapper.updateMenuAndChildrenIsDelete(list);
            this.mapper.syncTreeDeep();
            this.mapper.syncTreeHasChildren();
            // 发布Permission 变更通知
            UserPermissionChangeMessage message = new UserPermissionChangeMessage(null, true);
            redisService.sendPermissionChangeMsg(message);
        }
    }

    /**
     * 列表
     *
     * @param dto dto
     * @return {@link List}<{@link SysMenuVO}>
     */
    @Override
    public List<SysMenuVO> menuList(SysMenuListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().eq(SysMenu::getDelFlag, "F")
                                           /* .orderBy(SYS_MENU.DEEP.asc()) */
                                           .orderBy(SysMenuTableDef.SYS_MENU.SORT.asc());

        wrapper.ne(SysMenu::getMenuTypeCd, "1002003", !dto.isShowButton());
        wrapper.eq(SysMenu::getType, dto.getType(), StringUtils.isNotBlank(dto.getType()));
        wrapper.orderBy(SysMenu::getSort).asc();
        // 菜单全部数据
        List<SysMenu> list = list(wrapper);
        List<SysMenuVO> treeList = new ArrayList<>();
        // 构建树形
        for (SysMenuVO rootNode : getRootNodes(list)) {
            SysMenuVO menuVO = BeanCopyUtils.copy(rootNode, SysMenuVO.class);
            SysMenuVO.Meta meta = BeanCopyUtils.copy(rootNode, SysMenuVO.Meta.class);
            menuVO.setMeta(meta);
            SysMenuVO childrenNode = getChildrenNode(menuVO, list);
            treeList.add(childrenNode);
        }
        return treeList;
    }

    @Override
    public List<SysMenuVO> findMenuListByUserId(Long userId, String menuType) {
        List<SysMenuVO> treeList = new ArrayList<>();

        // 查询用户具有的menu_id
        List<String> menuIds = sysUserRoleMapper.queryMenuIdByUserId(userId);
        if (Utils.isNotNull(menuIds)) {
            // 菜单全部数据(当前用户下的)
            QueryWrapper wrapper = QueryWrapper.create();

            // 管理员默认获取所有菜单
            wrapper.in(SysMenu::getId, menuIds, !LoginUtils.isAdmin())
                   .eq(SysMenu::getDelFlag, DeleteFlag.F.getCode())
                   .eq(SysMenu::getType, menuType, !MenuMode.Common.getCode().equals(menuType))
                   .ne(SysMenu::getMenuTypeCd, "1002003")
                   .or(qw -> {
                       qw.eq(SysMenu::getType, MenuMode.Common.getCode());
                   })
                   .orderBy(SysMenu::getSort).asc().orderBy(SysMenu::getDeep).asc();
            List<SysMenu> list = list(wrapper);
            // 构建树形
            for (SysMenuVO rootNode : getRootNodes(list)) {
                SysMenuVO menuVO = BeanCopyUtils.copy(rootNode, SysMenuVO.class);
                SysMenuVO.Meta meta = BeanCopyUtils.copy(rootNode, SysMenuVO.Meta.class);
                meta.setIsLink(("T").equals(meta.getIsLink()) ? menuVO.getRedirect() : "");
                menuVO.setMeta(meta);
                SysMenuVO childrenNode = getChildrenNode(menuVO, list);
                treeList.add(childrenNode);
            }
        }
        return treeList;
    }

    @Override
    public List<MenuTreeVO> getSimpleMenuTree(String nodeId) {
        // 创建根目录节点并将所有数据包裹在其中
        MenuTreeVO root = new MenuTreeVO();
        root.setId("0"); // 根目录ID通常为0
        root.setPid("-1"); // 设置一个无效的值作为根目录的PID
        root.setTitle("根目录"); // 根目录的标题

        QueryWrapper wrapper = QueryWrapper.create().eq(SysMenu::getDelFlag, "F").ne(SysMenu::getMenuTypeCd, "1002003") // 排除按钮
                                           .orderBy(SYS_MENU.DEEP.asc()).orderBy(SysMenuTableDef.SYS_MENU.SORT.asc());
        List<SysMenu> list = list(wrapper);
        List<MenuTreeVO> menuTreeVOS = BeanCopyUtils.copyList(list, MenuTreeVO.class);
        return TreeUtils.buildTree(menuTreeVOS, root, nodeId);
    }

    @Override
    public List<MenuTreeVO> getMenuTreeVOS(String nodeId, boolean isShowButton) {
        List<String> childrenIds = new ArrayList<>();
        if (nodeId != null && !nodeId.equals("0")) {
            childrenIds = this.mapper.getMenuAndChildrenIds(nodeId, isShowButton);
        }
        List<SysMenuVO> sysMenuVOS;
        if (!childrenIds.isEmpty()) {
            sysMenuVOS = menuListTree(childrenIds);
        }
        else {
            SysMenuListDTO dto = new SysMenuListDTO();
            dto.setShowButton(isShowButton);
            sysMenuVOS = menuList(dto);
        }
        return BeanCopyUtils.copyList(sysMenuVOS, MenuTreeVO.class);
    }

    @Override
    public List<MenuTreeVO> queryRoleMenuTree(boolean isShowButton) {
        SysMenuListDTO dto = new SysMenuListDTO();
        dto.setShowButton(isShowButton);
        List<SysMenuVO> sysMenuVOS = menuList(dto);
        return BeanCopyUtils.copyList(sysMenuVOS, MenuTreeVO.class);
    }

    @Override
    public String exportMenuSql(SelectIdsDTO dto) {
        String generatedContent = "";
        if (Utils.isNotNull(dto.getStringIds())) {
            // 递归查询下边的子节点id
            List<String> list = this.mapper.selectMenuAndChildrenIds(dto.getStringIds());
            QueryWrapper queryWrapper = QueryWrapper.create().in(SysMenu::getId, list).orderBy(SysMenu::getDeep).asc().orderBy(SysMenu::getSort).asc();
            List<SysMenu> sysMenuList = list(queryWrapper);
            if (Utils.isNotNull(sysMenuList)) {
                Map<String, Object> dataModel = new HashMap<>();
                dataModel.put("sysMenuList", sysMenuList);
                try (StringWriter writer = new StringWriter()) {
                    Template template = generatorTableService.getMenuSqlTemplate();
                    template.process(dataModel, writer);
                    generatedContent = writer.toString();
                } catch (IOException | TemplateException e) {
                    log.error("exportMenuSql error", e);
                }
            }
        }
        return generatedContent;
    }

    /**
     * 详情
     *
     * @return {@link SysMenuVO}
     */
    @Override
    public SysMenu detail(String id) {
        SysMenu menu = getById(id);
        CommonResponseEnum.INVALID_ID.assertNull(menu);
        return menu;
    }

    /**
     * 验证是否有权限标识
     *
     * @param dto dto
     * @return 权限标识对象
     */
    @Override
    public MenuPermissionVO hasExistsPermissions(MenuPermissionDTO dto) {
        MenuPermissionVO permissionVO = new MenuPermissionVO();
        if (dto.getPermissions() == null || dto.getPermissions().isEmpty()) {
            return permissionVO;
        }
        QueryWrapper wrapper = QueryWrapper.create().ne(SysMenu::getId, dto.getId()).eq(SysMenu::getPermissions, dto.getPermissions());
        long count = count(wrapper);
        permissionVO.setPermissionCount((int) count);
        return permissionVO;
    }

    /**
     * 查询权限按钮
     *
     * @return 权限集合
     */
    @Override
    public List<String> findPermission() {
        return sysUserRoleMapper.queryPermissionByUserId(StpUtil.getLoginIdAsLong());
    }

    @Override
    public List<String> findPermissionsByUserId(Long userId) {
        QueryWrapper queryWrapper = QueryWrapper.create().select(QueryMethods.distinct(SYS_MENU.PERMISSIONS)).from(SYS_MENU).leftJoin(SYS_ROLE_MENU)
                                                .on(SYS_MENU.ID.eq(SYS_ROLE_MENU.MENU_ID)).leftJoin(SYS_USER_ROLE)
                                                .on(SYS_ROLE_MENU.ROLE_ID.eq(SYS_USER_ROLE.ROLE_ID))
                                                .where(SYS_USER_ROLE.USER_ID.eq(userId)).where(SYS_MENU.PERMISSIONS.isNotNull()).where(SYS_MENU.PERMISSIONS.ne(""));
        return listAs(queryWrapper, String.class);
    }

    @Override
    public List<String> findAllPermissions() {
        QueryWrapper queryWrapper = QueryWrapper.create().select(QueryMethods.distinct(SYS_MENU.PERMISSIONS)).from(SYS_MENU).eq(SysMenu::getDelFlag, "F")
                                                .isNotNull(SysMenu::getPermissions).ne(SysMenu::getPermissions, "");

        return listAs(queryWrapper, String.class);
    }

    @Override
    public Map<String, String> getBtnMenuByPermissions(Collection<String> permissions) {
        Map<String, String> btnMenuMap = new HashMap<>();
        if (permissions.isEmpty()) {
            return btnMenuMap;
        }
        try {
            QueryWrapper wrapper = QueryWrapper.create().from(SYS_MENU).where(SYS_MENU.PERMISSIONS.in(permissions));
            List<SysMenu> list = list(wrapper);
            if (list.isEmpty()) {
                return btnMenuMap;
            }
            Set<String> pids = list.stream().map(SysMenu::getPid).collect(Collectors.toSet());
            QueryWrapper checkWrapper = QueryWrapper.create().select(SYS_MENU.ID).from(SYS_MENU).where(SYS_MENU.ID.in(pids));
            List<String> existsMenuIds = listAs(checkWrapper, String.class);
            for (SysMenu menu : list) {
                if (existsMenuIds.contains(menu.getPid()) || existsMenuIds.contains(menu.getId())) { // 过滤脏数据
                    String key = menu.getPermissions();
                    String value;
                    if (("1002002").equals(menu.getMenuTypeCd())) {
                        value = menu.getId();
                    }
                    else {
                        value = menu.getPid();
                    }
                    btnMenuMap.put(key, value);
                }
            }
        } catch (Exception e) {
            log.error(" sync menuButton info err ", e);
        }
        return btnMenuMap;
    }

    @Override
    public List<MenuTreeVO> queryDataRoleMenu() {
        QueryWrapper wrapper = QueryWrapper.create().where(SYS_MENU.USE_DATA_SCOPE.eq("T")).where(SYS_MENU.MENU_TYPE_CD.eq("1002002"));
        List<SysMenu> list = list(wrapper);
        return BeanCopyUtils.copyList(list, MenuTreeVO.class);
    }

    @Override
    public void changeMenuDataScope(String menuId) {
        QueryWrapper wrapper = QueryWrapper.create().where(SYS_MENU.ID.eq(menuId));
        SysMenu menu = getOne(wrapper);
        CommonResponseEnum.INVALID_ID.assertNull(menu);
        if (DeleteFlag.F.getCode().equals(menu.getUseDataScope())) {
            menu.setUseDataScope("T");
        }
        else {
            menu.setUseDataScope("F");
        }
        updateById(menu);
        List<Long> changeUserIds = QueryChain.of(SysUserDataRole.class).select(SYS_USER_DATA_ROLE.USER_ID).from(SYS_USER_DATA_ROLE).leftJoin(SYS_DATA_ROLE_MENU)
                                             .on(SYS_DATA_ROLE_MENU.ROLE_ID.eq(SYS_USER_DATA_ROLE.ROLE_ID)).where(SYS_DATA_ROLE_MENU.MENU_ID.eq(menuId))
                                             .listAs(Long.class);
        eventPublisher.publish(new PermissionChangeEvent(this, new PermissionMeta(changeUserIds)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void genButtons(GenButtonDTO dto) {
        List<String> dbMenus = mapper.selectMenuAndChildrenIds(dto.getIds());
        dbMenus.removeAll(dto.getIds());
        if (CollectionUtils.isNotEmpty(dbMenus)) {
            throw new BusinessException("已经存在菜单了" + dbMenus);
        }
        Map<String, String> permMap = new HashMap<>();
        permMap.put("create", "新增");
        permMap.put("update", "修改");
        permMap.put("remove", "删除");
        permMap.put("import", "导入");
        permMap.put("export", "导出");

        for (String id : dto.getIds()) {
            SysMenu sysMenu = mapper.selectOneById(id);
            String permissions = sysMenu.getPermissions();
            String code = dto.getCode();
            if (StringUtils.isBlank(permissions)) {
                sysMenu.setPermissions(code + "." + "query_table");
                mapper.update(sysMenu);
            }
            List<SysMenu> menuList = permMap.entrySet().stream().map(permEntry -> {
                SysMenu menu = new SysMenu();
                menu.setId(Utils.generateUUIDs());
                menu.setPid(sysMenu.getId());
                menu.setPath("");
                menu.setName("");
                menu.setTitle(permEntry.getValue());
                menu.setType("Admin");
                menu.setIcon("");
                menu.setComponent("");
                menu.setRedirect("");
                menu.setSort(300);
                menu.setDeep(sysMenu.getDeep() + 1);
                menu.setMenuTypeCd("1002003");
                menu.setPermissions(code + "." + permEntry.getKey());
                menu.setIsHidden("F");
                menu.setHasChildren("F");
                menu.setIsLink("F");
                menu.setIsFull("F");
                menu.setIsAffix("F");
                menu.setIsKeepAlive("F");
                menu.setCreateTime(LocalDateTime.now());
                menu.setUpdateTime(LocalDateTime.now());
                menu.setDelFlag("F");
                menu.setUseDataScope("F");
                menu.setDeleteTime(LocalDateTime.now());
                return menu;
            }).toList();
            mapper.insertBatch(menuList);
        }
    }

    /**
     * 菜单属性查询(排除自己和自己的子节点)
     *
     * @param excludingIds 排除的id
     * @return 菜单属性
     */
    private List<SysMenuVO> menuListTree(List<String> excludingIds) {
        QueryWrapper wrapper = QueryWrapper.create().notIn(SysMenu::getId, excludingIds).ne(SysMenu::getMenuTypeCd, "10023").orderBy(SysMenu::getDeep).asc()
                                           .orderBy(SysMenu::getSort).asc().eq(SysMenu::getDelFlag, "F");

        // 菜单全部数据
        List<SysMenu> list = list(wrapper);
        List<SysMenuVO> treeList = new ArrayList<>();
        // 构建树形
        for (SysMenuVO rootNode : getRootNodes(list)) {
            SysMenuVO menuVO = BeanCopyUtils.copy(rootNode, SysMenuVO.class);
            SysMenuVO.Meta meta = BeanCopyUtils.copy(rootNode, SysMenuVO.Meta.class);
            menuVO.setMeta(meta);
            SysMenuVO childrenNode = getChildrenNode(menuVO, list);
            treeList.add(childrenNode);
        }
        return treeList;
    }

    /**
     * 是否是根节点
     *
     * @param pid 父级Id
     * @return true:是根节点
     */
    private boolean isRoot(String pid) {
        return pid == null || pid.equals("0");
    }

    /**
     * 获取父级跟节点
     *
     * @param list 菜单列表
     * @return 父级跟节点菜单列表
     */
    private List<SysMenuVO> getRootNodes(List<SysMenu> list) {
        List<SysMenuVO> rootList = new ArrayList<>();
        for (SysMenu sysMenu : list) {
            // 找到所有父级节点
            if (sysMenu.getPid() == null || sysMenu.getPid().equals("0")) {
                SysMenuVO sysMenuTreeVO = BeanCopyUtils.copy(sysMenu, SysMenuVO.class);
                rootList.add(sysMenuTreeVO);
            }
        }
        return rootList;
    }

    private SysMenuVO getChildrenNode(SysMenuVO sysMenu, List<SysMenu> menuList) {
        List<SysMenuVO> childrenList = new ArrayList<>();
        for (SysMenu menu : menuList) {
            if (menu.getPid().equals(sysMenu.getId())) {
                SysMenuVO childrenNode = BeanCopyUtils.copy(menu, SysMenuVO.class);
                SysMenuVO.Meta meta = BeanCopyUtils.copy(menu, SysMenuVO.Meta.class);
                meta.setIsLink(("T").equals(meta.getIsLink()) ? childrenNode.getRedirect() : "");
                childrenNode.setMeta(meta);
                childrenList.add(getChildrenNode(childrenNode, menuList));
            }
        }
        sysMenu.setChildren(childrenList);
        return sysMenu;
    }
}
