user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
error_log /var/log/nginx/error.local.log notice;
pid /var/run/nginx.pid;
stream {
    include /etc/nginx/streams-enabled/*;
}
events {
    # 优化连接数和处理模式
    worker_connections 4096;
    use epoll;
    multi_accept on;
    accept_mutex off;
}
http {
    # 基础配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    # 字符集
    charset utf-8;
    # 隐藏nginx版本信息
    server_tokens off;
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
        '$status $body_bytes_sent "$http_referer" '
        '"$http_user_agent" "$http_x_forwarded_for" '
        'rt=$request_time uct="$upstream_connect_time" '
        'uht="$upstream_header_time" urt="$upstream_response_time"';
    access_log /var/log/nginx/access.log main;
    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 100;
    client_max_body_size 100M;
    client_body_buffer_size 10M;
    client_header_buffer_size 4k;
    large_client_header_buffers 4 8k;
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    # 缓存配置
    open_file_cache max=10000 inactive=60s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;
    # SSL通用配置
    ssl_protocols TLSv1.2 TLSv1.3;# 移除不安全的TLS 1.0和1.1
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    # 代理缓存配置
    proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m max_size=10g
        inactive=60m use_temp_path=off;
    # 通用代理配置
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Host $http_host;
    proxy_set_header X-Forwarded-Port $server_port;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Ssl on;
    # 代理超时配置
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    proxy_busy_buffers_size 8k;
    include /etc/nginx/conf.d/*;
    include /etc/nginx/sites-enabled/*;

    # cms - 端口 7612 -> 7611
    server {
        server_name glowxq.com api.glowxq.com;
        listen 7612 ssl;
        http2 on;
        ssl_certificate /my/ssl/glowxq.com/cert.pem;
        ssl_certificate_key /my/ssl/glowxq.com/cert.key;
        location / {
            proxy_pass http://127.0.0.1:7611/;
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 10s;
        }
        location /health {
            access_log off;
            return 200 "healthy port 7612->7611\n";
            add_header Content-Type text/plain;
        }
    }

    # oj - 端口 7201 -> 7101
    server {
        server_name glowxq.com;
        listen 7201 ssl;
        http2 on;
        ssl_certificate /my/ssl/glowxq.com/cert.pem;
        ssl_certificate_key /my/ssl/glowxq.com/cert.key;
        location / {
            proxy_pass http://127.0.0.1:7101/;
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 10s;
        }
        location /health {
            access_log off;
            return 200 "healthy port 7201->7101\n";
            add_header Content-Type text/plain;
        }
    }

    # wingman - 端口 7202 -> 7102
    server {
        server_name glowxq.com;
        listen 7202 ssl;
        http2 on;
        ssl_certificate /my/ssl/glowxq.com/cert.pem;
        ssl_certificate_key /my/ssl/glowxq.com/cert.key;
        location / {
            proxy_pass http://127.0.0.1:7102/;
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 10s;
        }
        location /health {
            access_log off;
            return 200 "healthy port 7202->7102\n";
            add_header Content-Type text/plain;
        }
    }
    # rescue - 端口 7203 -> 7103
    server {
        server_name glowxq.com;
        listen 7203 ssl;
        http2 on;
        ssl_certificate /my/ssl/glowxq.com/cert.pem;
        ssl_certificate_key /my/ssl/glowxq.com/cert.key;
        location / {
            proxy_pass http://127.0.0.1:7103/;
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 10s;
        }
        location /health {
            access_log off;
            return 200 "healthy port 7203->7103\n";
            add_header Content-Type text/plain;
        }
    }
    # 服务器块 - 端口 7204 -> 7104
    server {
        server_name glowxq.com;
        listen 7204 ssl;
        http2 on;
        ssl_certificate /my/ssl/glowxq.com/cert.pem;
        ssl_certificate_key /my/ssl/glowxq.com/cert.key;
        location / {
            proxy_pass http://127.0.0.1:7104/;
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 10s;
        }
        location /health {
            access_log off;
            return 200 "healthy port 7204->7104\n";
            add_header Content-Type text/plain;
        }
    }

    # latex - 端口 7205 -> 7105
    server {
        server_name glowxq.com;
        listen 7205 ssl;
        http2 on;
        ssl_certificate /my/ssl/glowxq.com/cert.pem;
        ssl_certificate_key /my/ssl/glowxq.com/cert.key;
        location / {
            proxy_pass http://127.0.0.1:7105/;
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 10s;
        }
        location /health {
            access_log off;
            return 200 "healthy port 7205->7105\n";
            add_header Content-Type text/plain;
        }
    }

    # share - 端口 7206 -> 7106
    server {
        server_name glowxq.com;
        listen 7206 ssl;
        http2 on;
        ssl_certificate /my/ssl/glowxq.com/cert.pem;
        ssl_certificate_key /my/ssl/glowxq.com/cert.key;
        location / {
            proxy_pass http://127.0.0.1:7106/;
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 10s;
        }
        location /health {
            access_log off;
            return 200 "healthy port 7205->7105\n";
            add_header Content-Type text/plain;
        }
    }

    # oj.glowxq.com HTTPS配置 - 端口 443 -> 7301
    server {
        server_name oj.glowxq.com;
        listen 443 ssl;
        http2 on;
        ssl_certificate /my/ssl/glowxq.com/cert.pem;
        ssl_certificate_key /my/ssl/glowxq.com/cert.key;

        location / {
            proxy_pass http://127.0.0.1:7301/;
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 10s;
        }

        location /health {
            access_log off;
            return 200 "healthy oj.glowxq.com->7301\n";
            add_header Content-Type text/plain;
        }
    }

    # wingman.glowxq.com HTTPS配置 - 端口 443 -> 7302
    server {
        server_name wingman.glowxq.com;
        listen 443 ssl;
        http2 on;
        ssl_certificate /my/ssl/glowxq.com/cert.pem;
        ssl_certificate_key /my/ssl/glowxq.com/cert.key;

        location / {
            proxy_pass http://127.0.0.1:7302/;
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 10s;
        }

        location /health {
            access_log off;
            return 200 "healthy wingman.glowxq.com->7302\n";
            add_header Content-Type text/plain;
        }
    }

    # HTTP重定向到HTTPS (可选)
    server {
        listen 80;
        server_name oj.glowxq.com wingman.glowxq.com;
        return 301 https://$server_name$request_uri;
    }

    # nginxui
    server {
        listen 6331;
        location /health {
            access_log off;
            return 200 "healthy nginxui->6331\n";
            add_header Content-Type text/plain;
        }
    }
}