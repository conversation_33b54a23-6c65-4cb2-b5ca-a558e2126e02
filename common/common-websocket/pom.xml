<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.glowxq</groupId>
        <artifactId>common</artifactId>
        <version>${revision}</version>
    </parent>

    <groupId>com.glowxq.websocket</groupId>
    <artifactId>common-websocket</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.glowxq</groupId>
            <artifactId>common-core</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.glowxq</groupId>
            <artifactId>common-db-redis</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-jwt</artifactId>
        </dependency>
    </dependencies>


</project>