# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file

version: 2
updates:
  - package-ecosystem: "maven" # See documentation for possible values
    directory: "/glowxq-dependencies" # Location of package manifests
    schedule:
      interval: "weekly"
    target-branch: dev    # 指定要检查的目标分支
    open-pull-requests-limit: 20  # 限制同时打开的PR数量