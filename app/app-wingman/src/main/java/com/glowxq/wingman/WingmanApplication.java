package com.glowxq.wingman;

import com.glowxq.core.common.constant.Constant;
import com.glowxq.mysql.FlywayProperties;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableRetry
@ComponentScan(basePackages = {Constant.BASE_PACKAGE})
@SpringBootApplication
@EnableAspectJAutoProxy
@EnableScheduling
@RequiredArgsConstructor
public class WingmanApplication {

    @Getter
    private static String version;

    private final FlywayProperties flywayProperties;

    private final Flyway frameworkFlyway;

    private final Flyway businessFlyway;

    @Value("${app.version}")
    private String appVersion;

    private static void setVersion(String appVersion) {
        WingmanApplication.version = appVersion;
    }

    public static void main(String[] args) {
        SpringApplication.run(WingmanApplication.class, args);
        String template = """
                             ___      _                             __ _                       _              __ _                         \s
                            / __|    | |     ___   __ __ __ __ __  / _` |     o O O __ __ __  (_)    _ _     / _` |  _ __    __ _    _ _   \s
                           | (_ |    | |    / _ \\  \\ V  V / \\ \\ /  \\__, |    o      \\ V  V /  | |   | ' \\    \\__, | | '  \\  / _` |  | ' \\  \s
                            \\___|   _|_|_   \\___/   \\_/\\_/  /_\\_\\   __|_|   TS__[O]  \\_/\\_/  _|_|_  |_||_|   |___/  |_|_|_| \\__,_|  |_||_| \s
                          _|""\"""|_|""\"""|_|""\"""|_|""\"""|_|""\"""|_|""\"""| {======|_|""\"""|_|""\"""|_|""\"""|_|""\"""|_|""\"""|_|""\"""|_|""\"""|\s
                          "`-0-0-'"`-0-0-'"`-0-0-'"`-0-0-'"`-0-0-'"`-0-0-'./o--000'"`-0-0-'"`-0-0-'"`-0-0-'"`-0-0-'"`-0-0-'"`-0-0-'"`-0-0-'\s
                                                    ------------------%s  (v%s)-------------------
                          """;
        String result = String.format(template, "glowxq-wingman", getVersion());
        System.out.println(result);
    }

    @PostConstruct
    public void init() {
        setVersion(appVersion); // 通过辅助方法设置静态字段
        FlywayProperties.FlywayConfig business = flywayProperties.getBusiness();
        FlywayProperties.FlywayConfig framework = flywayProperties.getFramework();
        if (framework.isEnabled()) {
            frameworkFlyway.migrate();
        }
        if (business.isEnabled()) {
            businessFlyway.migrate();
        }
    }
}