---
language: C
src_path: main.c
exe_path: main
compile:
  env: default
  command: /usr/bin/gcc -DONLINE_JUDGE -w -fmax-errors=1 -std=c11 {src_path} -lm -o {exe_path}
  maxCpuTime: 5s
  maxRealTime: 10s
  maxMemory: 256mb
run:
  env: default
  command: /w/{exe_path}

---
language: C With O2
src_path: main.c
exe_path: main
compile:
  env: default
  command: /usr/bin/gcc -DONLINE_JUDGE -O2 -w -fmax-errors=1 -std=c11 {src_path} -lm -o {exe_path}
  maxCpuTime: 5s
  maxRealTime: 10s
  maxMemory: 256mb
run:
  env: default
  command: /w/{exe_path}

---
language: C++
src_path: main.cpp
exe_path: main
compile:
  command: /usr/bin/g++ -DONLINE_JUDGE -w -fmax-errors=1 -std=c++14 {src_path} -lm -o {exe_path}
  env: default
  maxCpuTime: 10s
  maxRealTime: 20s
  maxMemory: 512mb
run:
  env: default
  command: /w/{exe_path}

---
language: C++ With O2
src_path: main.cpp
exe_path: main
compile:
  env: default
  command: /usr/bin/g++ -DONLINE_JUDGE -O2 -w -fmax-errors=1 -std=c++14 {src_path} -lm -o {exe_path}
  maxCpuTime: 10s
  maxRealTime: 20s
  maxMemory: 512mb
run:
  env: default
  command: /w/{exe_path}

---
language: C++ 17
src_path: main.cpp
exe_path: main
compile:
  env: default
  command: /usr/bin/g++ -DONLINE_JUDGE -w -fmax-errors=1 -std=c++17 {src_path} -lm -o {exe_path}
  maxCpuTime: 10s
  maxRealTime: 20s
  maxMemory: 512mb
run:
  env: default
  command: /w/{exe_path}

---
language: C++ 17 With O2
src_path: main.cpp
exe_path: main
compile:
  env: default
  command: /usr/bin/g++ -DONLINE_JUDGE -O2 -w -fmax-errors=1 -std=c++17 {src_path} -lm -o {exe_path}
  maxCpuTime: 10s
  maxRealTime: 20s
  maxMemory: 512mb
run:
  env: default
  command: /w/{exe_path}

---
language: C++ 20
src_path: main.cpp
exe_path: main
compile:
  env: default
  command: /usr/bin/g++ -DONLINE_JUDGE -w -fmax-errors=1 -std=c++2a {src_path} -lm -o {exe_path}
  maxCpuTime: 10s
  maxRealTime: 20s
  maxMemory: 512mb
run:
  env: default
  command: /w/{exe_path}

---
language: C++ 20 With O2
src_path: main.cpp
exe_path: main
compile:
  env: default
  command: /usr/bin/g++ -DONLINE_JUDGE -O2 -w -fmax-errors=1 -std=c++2a {src_path} -lm -o {exe_path}
  maxCpuTime: 10s
  maxRealTime: 20s
  maxMemory: 512mb
run:
  env: default
  command: /w/{exe_path}

---
language: Java
src_path: Main.java
exe_path: Main.jar
compile:
  env: default
  command: /bin/bash -c "javac -encoding utf-8 {src_path} && jar -cvf {exe_path} *.class"
  maxCpuTime: 10s
  maxRealTime: 20s
  maxMemory: 512mb
run:
  env: default
  command: /usr/bin/java -Dfile.encoding=UTF-8 -cp /w/{exe_path} Main

---
language: Python2
src_path: main.py
exe_path: main.pyc
compile:
  env: default
  command: /usr/bin/python -m py_compile /w/{src_path}
  maxCpuTime: 5s
  maxRealTime: 10s
  maxMemory: 256mb
run:
  env: default
  command: /usr/bin/python /w/{exe_path}

---
language: Python3
src_path: main.py
exe_path: __pycache__/main.cpython-37.pyc
compile:
  env: python3
  command: /usr/bin/python3.7 -m py_compile /w/{src_path}
  maxCpuTime: 5s
  maxRealTime: 10s
  maxMemory: 256mb
run:
  env: python3
  command: /usr/bin/python3.7 /w/{exe_path}

---
language: PyPy2
src_path: main.py
exe_path: __pycache__/main.pypy-73.pyc
compile:
  env: default
  command: /usr/bin/pypy -m py_compile /w/{src_path}
  maxCpuTime: 5s
  maxRealTime: 10s
  maxMemory: 256mb
run:
  env: default
  command: /usr/bin/pypy /w/{exe_path}

---
language: PyPy3
src_path: main.py
exe_path: __pycache__/main.pypy39.pyc
compile:
  env: python3
  command: /usr/bin/pypy3 -m py_compile /w/{src_path}
  maxCpuTime: 5s
  maxRealTime: 10s
  maxMemory: 256mb
run:
  env: python3
  command: /usr/bin/pypy3 /w/{exe_path}


---
language: Golang
src_path: main.go
exe_path: main
compile:
  env: golang_compile
  command: /usr/bin/go build -o {exe_path} {src_path}
  maxCpuTime: 5s
  maxRealTime: 10s
  maxMemory: 512mb
run:
  env: golang_run
  command: /w/{exe_path}

---
language: C#
src_path: Main.cs
exe_path: main
compile:
  env: default
  command: /usr/bin/mcs -optimize+ -out:{exe_path} {src_path}
  maxCpuTime: 5s
  maxRealTime: 10s
  maxMemory: 512mb
run:
  env: default
  command: /usr/bin/mono /w/{exe_path}

---
language: PHP
src_path: main.php
exe_path: main.php
run:
  env: default
  command: /usr/bin/php /w/{exe_path}

---
language: JavaScript Node
src_path: main.js
exe_path: main.js
run:
  env: default
  command: /usr/bin/node /w/{exe_path}

---
language: JavaScript V8
src_path: main.js
exe_path: main.js
run:
  env: default
  command: /usr/bin/jsv8/d8 /w/{exe_path}

---
language: Ruby
src_path: main.rb
exe_path: main.rb
run:
  env: default
  command: /usr/bin/ruby /w/{exe_path}

---
language: Rust
src_path: Main.cs
exe_path: main
compile:
  env: default
  command: /usr/bin/rustc -O -o {exe_path} {src_path}
  maxCpuTime: 5s
  maxRealTime: 10s
  maxMemory: 512mb
run:
  env: default
  command: /w/{exe_path}

# 以下为特殊评测或交互评测使用的语言
---
language: SPJ-C
src_path: spj.c
exe_path: spj
compile:
  env: default
  command: /usr/bin/gcc -DONLINE_JUDGE -O2 -w -fmax-errors=3 -std=c11 {src_path} -lm -o {exe_path}
  maxCpuTime: 10s
  maxRealTime: 20s
  maxMemory: 512mb
run:
  env: default
  command: /w/{exe_path} {std_input} {user_output} {std_output}

---
language: SPJ-C++
src_path: spj.cpp
exe_path: spj
compile:
  env: default
  command: /usr/bin/g++ -DONLINE_JUDGE -O2 -w -fmax-errors=3 -std=c++14 {src_path} -lm -o {exe_path}
  maxCpuTime: 15s
  maxRealTime: 30s
  maxMemory: 512mb
run:
  env: default
  command: /w/{exe_path} {std_input} {user_output} {std_output}

---
language: INTERACTIVE-C
src_path: interactive.c
exe_path: interactive
compile:
  env: default
  command: /usr/bin/gcc -DONLINE_JUDGE -O2 -w -fmax-errors=3 -std=c11 {src_path} -lm -o {exe_path}
  maxCpuTime: 10s
  maxRealTime: 20s
  maxMemory: 512mb
run:
  env: default
  command: /w/{exe_path} {std_input} {user_output} {std_output}

---
language: INTERACTIVE-C++
src_path: interactive.cpp
exe_path: interactive
compile:
  env: default
  command: /usr/bin/g++ -DONLINE_JUDGE -O2 -w -fmax-errors=3 -std=c++14 {src_path} -lm -o {exe_path}
  maxCpuTime: 15s
  maxRealTime: 30s
  maxMemory: 512mb
run:
  env: default
  command: /w/{exe_path} {std_input} {user_output} {std_output}
