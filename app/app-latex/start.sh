#!/bin/bash

# LaTeX Application 启动脚本
# 自动添加必要的JVM参数以解决Java模块系统访问限制

# 设置JVM参数
JVM_ARGS="--add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED"

# 获取jar文件路径（支持通配符匹配）
JAR_FILE=$(ls target/app-latex-*.jar 2>/dev/null | head -n1)

# 检查jar文件是否存在
if [ -z "$JAR_FILE" ] || [ ! -f "$JAR_FILE" ]; then
    echo "❌ 错误: 找不到jar文件 target/app-latex-*.jar"
    echo "📦 请先运行以下命令进行打包:"
    echo "   mvn clean package -DskipTests"
    exit 1
fi

# 显示启动信息
echo "🚀 启动 LaTeX Application..."
echo "📁 JAR文件: $JAR_FILE"
echo "⚙️  JVM参数: $JVM_ARGS"
echo "📝 日志级别: INFO"
echo ""

# 启动应用
java $JVM_ARGS -jar "$JAR_FILE" "$@"
