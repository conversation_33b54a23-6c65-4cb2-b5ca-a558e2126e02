#!/bin/bash
#
# LaTeX Application 自定义启动脚本
# 自动添加必要的JVM参数以解决Java模块系统访问限制
#

# 设置默认的JVM参数
DEFAULT_JVM_OPTS="--add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED"

# 如果JAVA_OPTS环境变量已设置，则合并参数
if [ -n "$JAVA_OPTS" ]; then
    JAVA_OPTS="$DEFAULT_JVM_OPTS $JAVA_OPTS"
else
    JAVA_OPTS="$DEFAULT_JVM_OPTS"
fi

# 导出JAVA_OPTS供Spring Boot使用
export JAVA_OPTS

# 调用默认的Spring Boot启动脚本
{{>launchScript}}
