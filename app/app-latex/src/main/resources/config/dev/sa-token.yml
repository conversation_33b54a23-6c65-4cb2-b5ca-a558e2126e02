# sa-token
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # 是否开启自动续签
  auto-renew: true
  # token固定超时 设为七天 (必定过期) 单位: 秒
  timeout: 604800
  # 多端不同 token 有效期 可查看 LoginHelper.loginByDevice 方法自定义
  # token最低活跃时间 (指定时间无操作就过期) 单位: 秒
  active-timeout: 86400
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # jwt秘钥
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz
  # 允许动态设置 token 有效期
  dynamic-active-timeout: true
  # 允许从 请求参数 读取 token
  is-read-body: false
  # 允许从 header 读取 token
  is-read-header: true
  # 关闭 cookie 鉴权 从根源杜绝 csrf 漏洞风险
  is-read-cookie: false
  # token前缀
  token-prefix: "Bearer"
  # 同一账号最大登录数量，-1代表不限
  max-login-count: -1
# 接口白名单
router:
  whitelist:
    - "/v3/api-docs/**"
    - "/doc.html"
    - "/webjars/**"
    - "/swagger-resources"
    - "/favicon.ico"
    - "/swagger-ui.html"
    - "/www/**"
    - "/notify/**"
    - "/captcha/status"
    # LaTeX转换工具页面和静态资源
    - "/latex/converter"
    - "/www/latex-converter"
    - "/www/latex-test"
    - "/static/**"
    - "/css/**"
    - "/js/**"
    - "/images/**"