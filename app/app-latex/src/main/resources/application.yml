spring:
  threads:
    virtual:
      # 启用虚拟线程
      enabled: true
  profiles:
    active: local
  config:
    import:
      - optional:classpath:/config/${spring.profiles.active}/flyway.yml
      - optional:classpath:/config/${spring.profiles.active}/knife4j.yml
      - optional:classpath:/config/${spring.profiles.active}/oss.yml
      - optional:classpath:/config/${spring.profiles.active}/mybatis-flex.yml
      - optional:classpath:/config/${spring.profiles.active}/mysql.yml
      - optional:classpath:/config/${spring.profiles.active}/page-helper.yml
      - optional:classpath:/config/${spring.profiles.active}/redis.yml
      - optional:classpath:/config/${spring.profiles.active}/sa-token.yml
      - optional:classpath:/config/${spring.profiles.active}/feishu.yml
      - optional:classpath:/config/${spring.profiles.active}/wechat.yml
      - optional:classpath:/config/${spring.profiles.active}/tenant.yml

  mvc:
    path-match:
      matching-strategy: ant_path_matcher
  application:
    name: glowxq-latex
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 15MB

app:
  version: @project.version@
  name: ${spring.application.name}
  environment: ${spring.profiles.active}
  business: latex
web:
  # 是否启用数据权限拼接
  data-scope:
    enabled: true
    # 数据逻辑实现最小验证（查询）单位：dept 部门（dept_scope字段）、user 用户 (create_id 字段),默认user。
    logic-min-unit: user
  debounce:
    # 是否启用防抖功能
    enabled: true
    # 全局防抖时间，单位毫秒（默认1000ms）
    global-lock-time: 500
    # 是否忽略GET请求
    ignore-get-method: true
  # 生成工具
  generator:
    overwrite: true
    path:
      # 前端项目地址
      web: D:\project\glowxq-nexus\glowxq-web
      # 后端项目地址，默认自动检测springboot项目路径，无需配置。
      api: D:\project\glowxq-nexus\glowxq-backend
    # 模块名，指定代码生成的模块
    module-name: business
    service-name: business-latex
    global:
      author: glowxq
      packages: com.glowxq.latex
  # 微信生态
  wechat:
    # 小程序
    mini:
      app-id: wx695108a5b0d90c66
      app-secret: 73ced9715ac069fb09425d5007d3d263
  cors:
    # 定义允许跨域请求的源（Origin）。可以设置为特定的域名、IP 地址或通配符。
    allowed-origins:
      - "*"
  # 飞书配置
  feishu:
    app:
      app-id: cli_a8f51764f7fed00e
      app-secret: N1KVylWBmhTWN6Ps3wFtcxJ4Gp4pkOB4

# LaTeX模块专用配置
latex:
  # 图片下载配置
  image-download:
    # 是否启用图片下载功能
    enabled: true
    # 最大并发下载数量（符合飞书API的QPS限制）
    max-concurrent: 5
    # 最大重试次数
    max-retries: 3
    # 单个下载任务超时时间（秒）
    timeout-seconds: 30
    # 默认下载目录
    download-dir: "./downloads/images"
    # 初始退避时间（毫秒）
    initial-backoff-ms: 1000
    # 最大退避时间（毫秒）
    max-backoff-ms: 16000

  # LaTeX转换配置
  converter:
    # 接口类型：latex_ocr（公式识别）或 general_ocr（通用识别）
    api-type: general_ocr  # 默认使用公式识别接口，保持向后兼容

    # SimpleTex API配置（已废弃，由api-type动态生成）
    api-url: https://server.simpletex.cn/api/latex_ocr
    # 用户授权令牌（UAT方式，从 https://simpletex.cn/user/center 获取）
    uat-token: kNhAHG24e5U0MNpFSAykpoGmZDS05j0LTrX5IIQaGek4iCbd9lekMW8LW8jDXCys
    # 请求超时时间（秒）
    timeout-seconds: 30
    # 最大重试次数
    max-retries: 3

    # 通用OCR专用配置（仅当 api-type=general_ocr 时生效）
    general-ocr:
      # 识别模式：auto（自动检测）, document（文档模式）, formula（公式模式）
      rec-mode: auto
      # 是否启用图片旋转矫正（基于0°，90°, 180°, 270°自动矫正）
      enable-img-rotation: false
      # 行内公式包裹符号
      inline-formula-wrapper: ["$", "$"]
      # 独立行公式包裹符号
      isolated-formula-wrapper: ["$$", "$$"]

  # 表格操作配置
  sheet-operation:
    # 是否启用事务性操作
    transaction-enabled: true
    # 操作失败时是否回滚
    rollback-on-failure: true
    # 批量操作的批次大小
    batch-size: 50
