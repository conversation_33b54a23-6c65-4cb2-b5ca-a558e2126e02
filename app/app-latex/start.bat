@echo off
REM LaTeX Application 启动脚本 (Windows)
REM 自动添加必要的JVM参数以解决Java模块系统访问限制

REM 设置JVM参数
set JVM_ARGS=--add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED

REM 查找jar文件
set JAR_FILE=
for %%f in (target\app-latex-*.jar) do set JAR_FILE=%%f

REM 检查jar文件是否存在
if "%JAR_FILE%"=="" (
    echo ❌ 错误: 找不到jar文件 target\app-latex-*.jar
    echo 📦 请先运行以下命令进行打包:
    echo    mvn clean package -DskipTests
    pause
    exit /b 1
)

if not exist "%JAR_FILE%" (
    echo ❌ 错误: jar文件不存在 %JAR_FILE%
    echo 📦 请先运行以下命令进行打包:
    echo    mvn clean package -DskipTests
    pause
    exit /b 1
)

REM 显示启动信息
echo 🚀 启动 LaTeX Application...
echo 📁 JAR文件: %JAR_FILE%
echo ⚙️  JVM参数: %JVM_ARGS%
echo 📝 日志级别: INFO
echo.

REM 启动应用
java %JVM_ARGS% -jar "%JAR_FILE%" %*
