package com.glowxq.rescue.user;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.glowxq.core.common.annotation.OperationLog;
import com.glowxq.core.common.constant.GlobalConstant;
import com.glowxq.core.common.entity.ApiPageResult;
import com.glowxq.core.common.entity.ApiResult;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.ModuleEnum;
import com.glowxq.core.datascope.DataScopeHandle;
import com.glowxq.rescue.user.dto.RescueUserCreateDTO;
import com.glowxq.rescue.user.dto.RescueUserUpdateDTO;
import com.glowxq.system.admin.pojo.dto.sysuser.SysUserListDTO;
import com.glowxq.system.admin.pojo.vo.sysuser.SysUserVO;
import com.glowxq.system.admin.service.SysUserService;
import com.glowxq.tenant.utils.TenantUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 系统用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-01
 */
@Tag(name = "用户管理")
@RestController
@RequestMapping("/rescue-user")
@RequiredArgsConstructor
public class RescueUserApi {

    private final SysUserService sysUserService;

    @Operation(summary = "添加用户")
    @SaCheckPermission(value = "sys.user.create_btn", orRole = GlobalConstant.SUPER_ROLE)
    @PostMapping
    @OperationLog(module = ModuleEnum.System)
    public ApiResult<Void> create(@Valid @RequestBody RescueUserCreateDTO dto) {
        sysUserService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改用户")
    @SaCheckPermission(value = "sys.user.update_btn", orRole = GlobalConstant.SUPER_ROLE)
    @PutMapping
    @OperationLog(module = ModuleEnum.System)
    public ApiResult<Void> update(@Valid @RequestBody RescueUserUpdateDTO dto) {
        sysUserService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "批量删除")
    @SaCheckPermission(value = "sys.user.delete_btn", orRole = GlobalConstant.SUPER_ROLE)
    @DeleteMapping
    @OperationLog(module = ModuleEnum.System)
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        sysUserService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "查询分页列表")
    @SaCheckPermission(value = "sys.user.query_table", orRole = GlobalConstant.SUPER_ROLE)
    @PostMapping("/listPage")
    @DataScopeHandle
    public ApiPageResult<PageResult<SysUserVO>> listPage(@RequestBody @Valid SysUserListDTO dto) {
        dto.setTenantId(TenantUtils.getTenantId());
        return ApiPageResult.success(sysUserService.page(dto));
    }

    @Operation(summary = "用户详情")
    @SaCheckPermission(value = "sys.user.query_table", orRole = GlobalConstant.SUPER_ROLE)
    @GetMapping("{id}")
    public ApiResult<SysUserVO> detail(@PathVariable Long id) {
        SysUserVO detail = sysUserService.detail(id);
        return ApiResult.success(detail);
    }

    @Operation(summary = "登录信息查询")
    @GetMapping("/userinfo")
    public ApiResult<SysUserVO> getUserInfo() {
        return ApiResult.success(sysUserService.getUserInfo());
    }
}
