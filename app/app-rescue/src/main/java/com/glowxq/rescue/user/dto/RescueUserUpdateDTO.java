package com.glowxq.rescue.user.dto;

import com.glowxq.core.common.entity.base.BaseEntity;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.system.admin.pojo.dto.sysuser.SysUserUpdateDTO;
import com.glowxq.system.admin.pojo.po.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * SysUserUpdateDTO
 *
 * <AUTHOR>
 * @since 2023/8/23
 */
@Data
@Schema(description = "SysUser修改DTO")
public class RescueUserUpdateDTO extends SysUserUpdateDTO {

    @Override
    public BaseEntity buildEntity() {
        return BeanCopyUtils.copy(this, SysUser.class);
    }
}
