# 微信支付配置
wechat:
  app-id: wx695108a5b0d90c66
  payment:
    # 微信分配的公众账号ID
    app-id: ${wechat.app-id}
    # 微信支付分配的商户号
    mch-id: 1716466089
    # 商户密钥，用于签名
    mch-key: xyghpp13402066610123456789123456
    # 服务商模式下的子商户公众账号ID
    sub-app-id:
    # 服务商模式下的子商户号
    sub-mch-id:
    # 结果通知
    notify-url: https://glowxq.com:7202/api
    # 支付结果通知URL
    pay-notify-path: ${wechat.payment.notify-url}/notify/wechat/payment
    # 退款结果通知URL
    refund-notify-path: ${wechat.payment.notify-url}/notify/wechat/refund
    # 商户APIv3密钥，用于APIv3签名
    api-v3-key: xyghpp13402066610123456789123456
    # 商户私钥路径，用于API签名
    private-key-path: classpath:cert/apiclient_key.pem
    # 商户证书路径，用于API验证
    private-cert-path: classpath:cert/apiclient_cert.pem
    # CA证书路径，用于SSL验证
    key-path: classpath:cert/apiclient_cert.p12
  # 小程序配置
  applet:
    # 微信分配的小程序AppID
    app-id: ${wechat.app-id}
    # 小程序密钥
    secret: 73ced9715ac069fb09425d5007d3d263
    # 消息加解密密钥
    token:
    # 消息加解密算法密钥
    aes-key:
    # 消息格式
    msg-data-format: JSON
  # 无限码配置
  wxa-code-unlimit:
    # 环境标识，开发环境或生产环境
    environment: release
    page: pages/Registration/index
    scene: code=xxx的形式
